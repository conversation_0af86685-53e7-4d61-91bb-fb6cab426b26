export const options = [
  {
    id: 1,
    title: "This is not an opportune time to invest in mobile home parks.",
  },
  {
    id: 2,
    title: "Now is the right time to think of investing in mobile home parks.",
  },
  {
    id: 3,
    title: "Gradually mobile home parks are concentrating to city centers.",
  },
  {
    id: 4,
    title:
      "Nowadays, most investors are up-to-date on mobile home parks' potential.",
  },
];

export const screen_selection = [
  {
    title: "Welcome screen",
  },
  {
    title: "Test Instructions screen",
  },
  {
    title: "Webcam screen",
  },
  {
    title: "Question screen",
  },
  {
    title: "Feedback screen",
  },
];

export const highestEducation = [
  {
    title: "Some High School",
  },
  {
    title: "High School Diploma / GED",
  },
  {
    title: "College",
  },
  {
    title: "Associate Degree",
  },
  {
    title: "Bachelor's Degree",
  },
  {
    title: "Master's Degree or Higher",
  },
];

export const experiences = [
  { title: "Software Engineer" },
  { title: "Data Scientist" },
  { title: "Product Manager" },
  { title: "UX Designer" },
  { title: "Frontend Developer" },
  { title: "Backend Developer" },
  { title: "Full Stack Developer" },
  { title: "DevOps Engineer" },
  { title: "Graphic Designer" },
  { title: "UI Designer" },
  { title: "Network Engineer" },
  { title: "Marketing Manager" },
  { title: "Sales Associate" },
  { title: "Business Analyst" },
  { title: "Financial Analyst" },
  { title: "Human Resources Manager" },
  { title: "Project Manager" },
  { title: "Quality Assurance Analyst" },
  { title: "Content Writer" },
  { title: "Customer Support Representative" },
  { title: "Operations Manager" },
  { title: "Accountant" },
  { title: "System Administrator" },
  { title: "UX Researcher" },
  { title: "Product Designer" },
  { title: "Machine Learning Engineer" },
  { title: "Database Administrator" },
  { title: "Business Development Manager" },
  { title: "Mobile App Developer" },
  { title: "Technical Writer" },
  { title: "Social Media Manager" },
  { title: "Network Administrator" },
  { title: "Data Analyst" },
  { title: "IT Manager" },
  { title: "Software Architect" },
  { title: "Sales Manager" },
  { title: "Digital Marketing Specialist" },
  { title: "Content Strategist" },
  { title: "Web Developer" },
  { title: "Customer Success Manager" },
  { title: "UX/UI Developer" },
  { title: "Financial Advisor" },
  { title: "Operations Coordinator" },
  { title: "Public Relations Specialist" },
  { title: "E-commerce Manager" },
  { title: "Business Consultant" },
  { title: "Legal Counsel" },
  { title: "Market Research Analyst" },
  { title: "Account Manager" },
  { title: "Software Tester" },
  { title: "Database Analyst" },
  { title: "Systems Analyst" },
  { title: "Network Security Analyst" },
  { title: "Customer Service Manager" },
  { title: "IT Support Specialist" },
  { title: "Frontend Designer" },
  { title: "IT Consultant" },
  { title: "Data Engineer" },
  { title: "Product Owner" },
  { title: "UX Research Assistant" },
  { title: "UI Developer" },
  { title: "Game Developer" },
  { title: "Financial Planner" },
  { title: "Operations Analyst" },
  { title: "Public Relations Manager" },
  { title: "E-commerce Specialist" },
  { title: "Business Manager" },
  { title: "Corporate Counsel" },
  { title: "Market Analyst" },
  { title: "Account Executive" },
  { title: "Test Engineer" },
  { title: "Database Administrator" },
  { title: "Systems Administrator" },
  { title: "Network Security Specialist" },
  { title: "Customer Support Manager" },
  { title: "IT Specialist" },
  { title: "Frontend Developer" },
  { title: "IT Analyst" },
  { title: "Data Scientist Assistant" },
  { title: "UI/UX Designer" },
  { title: "Mobile App Developer" },
  { title: "Financial Analyst Assistant" },
  { title: "Operations Assistant" },
  { title: "Public Relations Coordinator" },
  { title: "E-commerce Coordinator" },
  { title: "Business Analyst Assistant" },
  { title: "Legal Assistant" },
  { title: "Market Research Assistant" },
  { title: "Account Coordinator" },
  { title: "Software Quality Assurance Analyst" },
  { title: "Database Developer" },
  { title: "Systems Developer" },
  { title: "Network Security Developer" },
  { title: "Customer Support Representative" },
  { title: "IT Coordinator" },
  { title: "Frontend Developer Assistant" },
  { title: "IT Assistant" },
  { title: "Data Scientist Intern" },
  { title: "UI/UX Designer Assistant" },
  { title: "Mobile App Developer Assistant" },
  { title: "Financial Analyst Intern" },
  { title: "Operations Intern" },
  { title: "Public Relations Intern" },
  { title: "E-commerce Intern" },
  { title: "Business Analyst Intern" },
  { title: "Legal Intern" },
  { title: "Market Research Intern" },
  { title: "Account Manager Assistant" },
  { title: "Software Developer Assistant" },
  { title: "Systems Developer Assistant" },
  { title: "Network Security Developer Assistant" },
];

export const gender = [
  {
    title: "Women",
  },
  {
    title: "Man",
  },
  {
    title: "Nonbinary/Other",
  },
  {
    title: "Prefer not to answer",
  },
];

export const ethnicity = [
  {
    title: "Arab",
  },
  {
    title: "Asian",
  },
  {
    title: "Black, Caribbean, or African",
  },
  {
    title: "Hispanic or Latin American",
  },
  {
    title: "Mixed or multiple ethnic groups",
  },
  {
    title: "Pacific Islander",
  },
  {
    title: "Some other ethnicity",
  },
  {
    title: "White",
  },
];

export const language = [
  {
    title: "English",
  },
  {
    title: "Danish",
  },
  {
    title: "Dutch",
  },
  {
    title: "French",
  },
  {
    title: "German",
  },
  {
    title: "Italian",
  },
  {
    title: "Norwegian",
  },
  {
    title: "polish",
  },
  {
    title: "Portuguese (Brazil)",
  },
  {
    title: "Spanish",
  },
  {
    title: "Swedish",
  },
  {
    title: "Other",
  },
  {
    title: "Prefer not to answer",
  },
];

export const getStepsSettings = (t) => [
  {
    title: t("settingsSteps.editProfileTitle"),
    element: "#setting1",
    intro: (
      <div>
        <p>{t("settingsSteps.editProfileIntro1")}</p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.editProfileIntro2")}
        </p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.editProfileIntro3")}
        </p>
        <ul className="list-disc px-4" style={{ marginTop: "8px" }}>
          <li>{t("settingsSteps.editProfileList1")}</li>
          <li>{t("settingsSteps.editProfileList2")}</li>
          <li>{t("settingsSteps.editProfileList3")}</li>
          <li>{t("settingsSteps.editProfileList4")}</li>
        </ul>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.editProfileOutro")}
        </p>
      </div>
    ),
  },
  {
    title: t("settingsSteps.notificationPreferencesTitle"),
    element: "#setting2",
    intro: (
      <div>
        <p>{t("settingsSteps.notificationPreferencesIntro1")}</p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.notificationPreferencesIntro2")}
        </p>
        <ul className="list-disc px-4">
          <li>{t("settingsSteps.notificationPreferencesList1")}</li>
          <li>{t("settingsSteps.notificationPreferencesList2")}</li>
        </ul>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.notificationPreferencesOutro1")}
        </p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.notificationPreferencesOutro2")}
        </p>
      </div>
    ),
  },
  {
    title: t("settingsSteps.companyDetailsTitle"),
    element: "#setting3",
    intro: (
      <div>
        <p>{t("settingsSteps.companyDetailsIntro1")}</p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.companyDetailsIntro2")}
        </p>
        <ul className="list-disc px-4">
          <li>{t("settingsSteps.companyDetailsList1")}</li>
          <li>{t("settingsSteps.companyDetailsList2")}</li>
          <li>{t("settingsSteps.companyDetailsList3")}</li>
          <li>{t("settingsSteps.companyDetailsList4")}</li>
          <li>{t("settingsSteps.companyDetailsList5")}</li>
        </ul>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.companyDetailsOutro")}
        </p>
      </div>
    ),
  },
  {
    title: t("settingsSteps.teamManagementTitle"),
    element: "#setting4",
    intro: (
      <div>
        <p>{t("settingsSteps.teamManagementIntro1")}</p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.teamManagementIntro2")}
        </p>
        <ul className="list-disc px-4">
          <li>{t("settingsSteps.teamManagementList1")}</li>
          <li>{t("settingsSteps.teamManagementList2")}</li>
        </ul>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.teamManagementOutro")}
        </p>
      </div>
    ),
  },
  {
    title: t("settingsSteps.subscriptionTitle"),
    element: "#setting5",
    intro: (
      <div>
        <p>{t("settingsSteps.subscriptionIntro1")}</p>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.subscriptionIntro2")}
        </p>
        <ul className="list-disc px-4">
          <li>{t("settingsSteps.subscriptionList1")}</li>
          <li>{t("settingsSteps.subscriptionList2")}</li>
          <li>{t("settingsSteps.subscriptionList3")}</li>
          <li>{t("settingsSteps.subscriptionList4")}</li>
        </ul>
        <p style={{ marginTop: "8px" }}>
          {t("settingsSteps.subscriptionOutro")}
        </p>
      </div>
    ),
  },
  {
    intro: (
      <div>
        <p>
          {t("settingsSteps.tourComplete1")}
          <a
            href="https://dexta.io/contact"
            target="_blank"
            className="font-bold cursor-pointer underline"
          >
            {t("settingsSteps.tourCompleteLink")}
          </a>
        </p>
      </div>
    ),
  },
];

export const aspectRatios = [
  { label: "16:9", value: 16 / 9 },
  { label: "9:3", value: 9 / 2.5 },
  { label: "4:3", value: 4 / 3 },
  { label: "4:1", value: 4 / 1 },
  { label: "3:1", value: 3 / 1 },
];
