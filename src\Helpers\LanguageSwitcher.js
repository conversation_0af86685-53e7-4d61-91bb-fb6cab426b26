import React, { useEffect, useState, useRef } from "react"
import i18n from "../i18n"


export const LanguageSwitcher = () => {
  const languages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "es", name: "Spanish", flag: "🇪🇸" },
  ];
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const currentLang = i18n.language || "en";
    const found = languages.find((l) => l.code === currentLang);
    setSelectedLanguage(found || languages[0]);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => setIsOpen(!isOpen);
  const selectLanguage = (language) => {
    setSelectedLanguage(language);
    setIsOpen(false);
    i18n.changeLanguage(language.code);
  };

  return (
    <>
      {process.env.REACT_APP_LANGUAGE_SWITCH_ACCESS === "YES" && (
        <div className="relative my-auto" ref={dropdownRef}>
          <button
            className="flex items-center justify-center gap-1 text-black my-auto px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors bg-white min-w-[120px] border border-gray-200 shadow"
            onClick={toggleDropdown}
            aria-expanded={isOpen}
            aria-haspopup="true"
            type="button"
            style={{ zIndex: 60 }}
          >
            <span className="text-[13px] flex items-center leading-none mr-1" style={{ fontFamily: "Archia Semibold" }}>
              {selectedLanguage.name}
            </span>
            <svg
              width="16"
              height="16"
              fill="none"
              viewBox="0 0 24 24"
              className={`text-gray-500 flex-shrink-0 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
            >
              <path d="M7 10l5 5 5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
          {isOpen && (
            <div className="absolute right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
              <div className="py-1">
                {languages.map((language) => (
                  <button
                    key={language.code}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => selectLanguage(language)}
                    style={{ fontFamily: "Silka" }}
                    type="button"
                  >
                    <span className="mr-2">{language.flag}</span>
                    <span className="flex-grow text-left">{language.name}</span>
                    {selectedLanguage.code === language.code && (
                      <svg width="16" height="16" fill="none" viewBox="0 0 24 24" className="text-blue-500 ml-2"><path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};