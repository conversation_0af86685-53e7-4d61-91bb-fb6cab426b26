import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import { FaQuestionCircle } from "react-icons/fa";
import { FaChevronDown } from "react-icons/fa";
import { FaArrowLeft } from "react-icons/fa";
import { FaArrowRight } from "react-icons/fa";
import { FaInfoCircle } from "react-icons/fa";
import { FaExpandArrowsAlt } from "react-icons/fa";
import { FaCompressArrowsAlt } from "react-icons/fa";
import { FaRobot } from "react-icons/fa";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getUserData } from "../hooks/getUserData";
import { createCustomQuestions } from "../hooks/createCustomQuestions";
import http from "../../../../../http";
import info from "../../../../../Dexta_assets/helpIcon.png";
import eye from "../../../../../Dexta_assets/eye.png";
import bin from "../../../../../Dexta_assets/bin.png";
import { MdModeEditOutline } from "react-icons/md";
import { TimeData } from "./data";
import { createAIQuestion } from "../hooks/createAIQuestion";
import QuestionsModal from "./QuestionsModal";
import DeleteModal from "../../../../../Components/ConfirmationModals/DeleteModal";
import DeleteIcon from "../../../../../Dexta_assets/deleteIcon.png";

const CreateCustomTestAI = ({
	onGoBack,
	onNextStep,
	onAddQuestion,
	data,
	setData,
	questions,
	setQuestions,
	handleModulesIDS
}) => {
	const { t } = useTranslation();
	const [testName, setTestName] = useState("");
	const [testTime, setTestTime] = useState("");
	const [chatMessage, setChatMessage] = useState("");
	const [hasGeneratedQuestions, setHasGeneratedQuestions] = useState(false);
	const [selectedAnswers, setSelectedAnswers] = useState({});
	const [acceptedQuestions, setAcceptedQuestions] = useState([]);
	const [rejectedQuestions, setRejectedQuestions] = useState([]);
	const [expandedQuestions, setExpandedQuestions] = useState({});
	const [isChatExpanded, setIsChatExpanded] = useState(false);
	const [chatError, setChatError] = useState("");
	const [moduleCreated, setModuleCreated] = useState(false);
	const [createdModuleId, setCreatedModuleId] = useState(null);
	const [conversationId, setConversationId] = useState(null);
	const userID = localStorage.getItem("CP-USER-ID");
	const [timeDropdown, setTimeDropdown] = useState(false);
	const [timeTitle, setTimeTitle] = useState("");
	const TimeRef = useRef(null);

	// Edit functionality states (same as Questions.js)
	const [questionModal, setQuestionModal] = useState(false);
	const [showQuestion, setShowQuestion] = useState(false);
	const [questionIndex, setQuestionIndex] = useState(null);

	// Delete functionality states
	const [deleteModal, setDeleteModal] = useState(false);
	const [questionToDelete, setQuestionToDelete] = useState(null);
	const [deleteLoading, setDeleteLoading] = useState(false);
	const chatMessagesRef = useRef(null);
	const chatMessagesCompactRef = useRef(null);
	const assessmentID = localStorage.getItem("assessment_ID");

	// Get user data for profile photo
	const { data: userData } = useQuery({
		queryKey: ["userData", userID],
		queryFn: () => getUserData(userID),
	});

	const [chatMessages, setChatMessages] = useState([
		{
			id: 1,
			type: "ai",
			message: "Hello! I'm your Dexta AI Assistant. Please fill in the test name and select time limit first, then I can help you generate questions for your custom test.",
			timestamp: "08:16 AM"
		}
	]);

	console.log(chatMessage, "chatMessage")
	const [generatedQuestions, setGeneratedQuestions] = useState([]);

	// Check if user can use chatbot (has filled name and time)
	const canUseChatbot = testName.trim() && testTime && !isNaN(testTime);

	// Auto-scroll to bottom when new messages are added
	const scrollToBottom = () => {
		const activeRef = isChatExpanded ? chatMessagesRef : chatMessagesCompactRef;
		if (activeRef.current) {
			activeRef.current.scrollTop = activeRef.current.scrollHeight;
		}
	};

	// Scroll to bottom whenever chatMessages changes
	useEffect(() => {
		scrollToBottom();
	}, [chatMessages]);

	// Shuffle array function
	const shuffleArray = (array) => {
		const shuffled = [...array];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	};

	// Transform API response to our question structure
	const transformApiQuestions = (apiQuestions) => {
		return apiQuestions.map((q, index) => {
			// Create options with original indices for tracking
			const optionsWithIndex = q.options.map((opt, idx) => ({
				...opt,
				originalIndex: idx
			}));

			// Shuffle the options
			const shuffledOptions = shuffleArray(optionsWithIndex);

			return {
				id: index + 1,
				question: q.question,
				options: shuffledOptions, // Shuffled options
				correctAnswer: q.type === 'multiple_choice'
					? shuffledOptions.map((opt, idx) => opt.isCorrect ? idx : null).filter(idx => idx !== null)
					: shuffledOptions.findIndex(opt => opt.isCorrect),
				apiData: q, // Keep original API data for posting
				type: q.type,
				difficulty: q.difficulty
			};
		});
	};

	// Create custom module first
	const createModuleMutation = useMutation({
		mutationFn: async (data) => {
			const response = await createCustomQuestions(data);
			return response;
		},
		onSuccess: (response, variables) => {
			const sectionId = response?.data?.id;
			if (sectionId) {
				setModuleCreated(true);
				setCreatedModuleId(sectionId);
				// Call generate questions with the user message
				generateQuestionsMutation(variables.userMessage);
			}
		},
		onError: (error) => {
			console.error('Error creating module:', error);
			setChatError('Failed to create module. Please try again.');
			setChatMessages(prev => {
				const withoutLoading = prev.filter(msg => !msg.isLoading);
				const errorResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: "I'm sorry, I encountered an error while creating the module. Please try again.",
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
				};
				return [...withoutLoading, errorResponse];
			});
		}
	});

	// First API call - generate questions (only called once)
	const { mutate: generateQuestions, isLoading: isGeneratingQuestions } = useMutation(createAIQuestion, {
		onSuccess: (data) => {
			// Remove loading message first
			setChatMessages(prev => prev.filter(msg => !msg.isLoading));

			// Store conversation ID for future calls
			if (data.metadata && data.metadata.conversationId) {
				setConversationId(data.metadata.conversationId);
			}

			// Check if this is a clarification step
			if (data.metadata && data.metadata.step === 'clarification') {
				// Handle clarification questions
				const aiResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: "I need some more details to generate the perfect questions for you:",
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
					clarificationQuestions: data.questions
				};
				setChatMessages(prev => [...prev, aiResponse]);
			} else if (data.questions && data.questions.length > 0) {
				// Handle actual generated questions
				const transformedQuestions = transformApiQuestions(data.questions);
				setGeneratedQuestions(transformedQuestions);
				setHasGeneratedQuestions(true);

				// Initialize selectedAnswers with correct answers
				const initialAnswers = {};
				transformedQuestions.forEach(question => {
					if (question.type === 'multiple_choice') {
						// For multiple choice, select all correct options
						initialAnswers[question.id] = question.options
							.map((opt, idx) => opt.isCorrect ? idx : null)
							.filter(idx => idx !== null);
					} else {
						// For single choice, select the correct option
						initialAnswers[question.id] = question.options.findIndex(opt => opt.isCorrect);
					}
				});
				setSelectedAnswers(initialAnswers);

				const aiResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: `I've generated ${data.questions.length} questions for you based on your request. You can review them in the main content area.`,
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
				};
				setChatMessages(prev => [...prev, aiResponse]);
			} else {
				throw new Error('No questions generated from API');
			}
		},
		onError: (error) => {
			console.error('Error generating questions:', error);
			setChatError('Failed to generate questions. Please try again.');

			// Remove loading message and add error message
			setChatMessages(prev => {
				const withoutLoading = prev.filter(msg => !msg.isLoading);
				const errorResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: "I'm sorry, I encountered an error while generating questions. Please try again or check your connection.",
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
				};
				return [...withoutLoading, errorResponse];
			});
		}
	});

	// Continue conversation API call (used after first call)
	const { mutate: continueConversation, isLoading: isContinuingConversation } = useMutation({
		mutationFn: async ({ conversationId, message }) => {
			const response = await http.post(`/section/continue-conversation/${conversationId}`, {
				userResponse: message
			});
			return response.data;
		},
		onSuccess: (data) => {
			// Remove loading message first
			setChatMessages(prev => prev.filter(msg => !msg.isLoading));

			// Check if this is a clarification step
			if (data.metadata && data.metadata.step === 'clarification') {
				// Handle clarification questions
				const aiResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: "I need some more details to generate the perfect questions for you:",
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
					clarificationQuestions: data.questions
				};
				setChatMessages(prev => [...prev, aiResponse]);
			} else if (data.questions && data.questions.length > 0) {
				// Handle actual generated questions
				const transformedQuestions = transformApiQuestions(data.questions);
				setGeneratedQuestions(transformedQuestions);
				setHasGeneratedQuestions(true);

				// Initialize selectedAnswers with correct answers
				const initialAnswers = {};
				transformedQuestions.forEach(question => {
					if (question.type === 'multiple_choice') {
						// For multiple choice, select all correct options
						initialAnswers[question.id] = question.options
							.map((opt, idx) => opt.isCorrect ? idx : null)
							.filter(idx => idx !== null);
					} else {
						// For single choice, select the correct option
						initialAnswers[question.id] = question.options.findIndex(opt => opt.isCorrect);
					}
				});
				setSelectedAnswers(initialAnswers);

				const aiResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: `I've generated ${data.questions.length} questions for you based on your request. You can review them in the main content area.`,
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
				};
				setChatMessages(prev => [...prev, aiResponse]);
			} else {
				throw new Error('No questions generated from API');
			}
		},
		onError: (error) => {
			console.error('Error continuing conversation:', error);
			setChatError('Failed to continue conversation. Please try again.');

			// Remove loading message and add error message
			setChatMessages(prev => {
				const withoutLoading = prev.filter(msg => !msg.isLoading);
				const errorResponse = {
					id: Date.now() + 1,
					type: "ai",
					message: "I'm sorry, I encountered an error while processing your request. Please try again or check your connection.",
					timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
				};
				return [...withoutLoading, errorResponse];
			});
		}
	});


	// Generate questions using appropriate API
	const generateQuestionsMutation = (message) => {
		try {
			if (conversationId) {
				// Use continue conversation API
				continueConversation({ conversationId, message });
			} else {
				// Use initial generate questions API
				const data = {
					content: {
						prompt: message,
					},
					id: assessmentID
				};
				generateQuestions(data);
			}
		} catch (error) {
			console.error('Error generating questions:', error);
			setChatError('Failed to generate questions. Please try again.');
		}
	}

	// Helper function to create options (same as QuestionsModal.js)
	const createOption = async (option) => {
		try {
			let cleanObj = option;
			cleanObj = removeEmpty(cleanObj);
			return await http.post(`/options`, cleanObj).then((response) => {
				return response.data;
			});
		} catch (error) {
			console.log("🚀 ~ createOption ~ error:", error);
			throw error;
		}
	};

	// Helper function to remove empty values (same as QuestionsModal.js)
	function removeEmpty(obj) {
		return Object.entries(obj)
			.filter(([_, v]) => v != null && v !== "")
			.reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {});
	}

	// Post approved question to assessment with useMutation
	const postQuestionMutation = useMutation({
		mutationFn: async (question) => {
			console.log('Creating question:', question);
			console.log('Section ID (createdModuleId):', createdModuleId);
			console.log('Fallback section ID (data?.customQuestion):', data?.customQuestion);

			// Count correct answers to determine question type
			const correctCount = question.options.filter(opt => opt.isCorrect).length;

			// Create question data following the same structure as QuestionsModal.js
			const questionData = {
				type: correctCount > 1 ? "Multiple" : "Single",
				image: null, // AI questions don't have images
				figImage: null,
				status: "active",
				description: question.question,
				section: createdModuleId || data?.customQuestion, // Use the created module ID or fallback to existing section
				is_sample: false,
			};

			// Create the question
			const response = await http.post(`/questions`, questionData);
			const createdQuestion = response.data;

			// Create options for the question
			const options = question.options.map((option, index) => ({
				question: createdQuestion.id,
				title: option.text, // Map 'text' to 'title' for consistency with existing structure
				isCorrectOption: option.isCorrect, // Map 'isCorrect' to 'isCorrectOption'
				optionPosition: index,
				imageUrl: null,
				status: "active",
			}));

			// Create all options
			const optionSavedList = await Promise.all(
				options.map(opt => createOption(opt))
			);

			// Return the complete question with options
			return {
				...createdQuestion,
				options: optionSavedList,
			};
		},
		onSuccess: (createdQuestion, originalQuestion) => {
			console.log('Question created successfully:', createdQuestion);

			// Add to accepted questions using the database format (same as Questions.js)
			// This allows QuestionsModal to work properly for editing
			setAcceptedQuestions(prev => [...prev, createdQuestion]);

			// Remove from generated questions
			setGeneratedQuestions(prev => prev.filter(q => q.id !== originalQuestion.id));

			// Update the assessment data with the new question (same as QuestionsModal.js)
			if (setQuestions && questions) {
				const updatedQuestions = [createdQuestion, ...questions];
				setQuestions(updatedQuestions);

				// Update the data object to include the new question
				if (handleModulesIDS) {
					handleModulesIDS(updatedQuestions);
				}
			}

			// Don't add success message to chat - keep it clean
		},
		onError: (error, question) => {
			console.error('Error creating question:', error);
			// Add error message to chat
			const errorMessage = {
				id: Date.now(),
				type: "ai",
				message: `Failed to add question "${question.question.substring(0, 50)}...". Please try again.`,
				timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
			};
			setChatMessages(prev => [...prev, errorMessage]);
		}
	});

	const handleSendMessage = async () => {
		if (chatMessage.trim() && canUseChatbot) {
			const currentMessage = chatMessage; // Store message before clearing

			const newMessage = {
				id: Date.now(),
				type: "user",
				message: currentMessage,
				timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
			};

			// Add user message to chat
			setChatMessages(prev => [...prev, newMessage]);

			// Add loading message
			const loadingMessage = {
				id: Date.now() + 1,
				type: "ai",
				message: "I'm generating questions for you. Please wait...",
				timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
				isLoading: true
			};
			setChatMessages(prev => [...prev, loadingMessage]);
			setChatMessage("");

			// Check if module is already created
			if (moduleCreated && createdModuleId) {
				// Module already exists, just generate questions
				generateQuestionsMutation(currentMessage);
			} else {
				// First create the custom module, then generate questions
				const moduleData = {
					name: testName,
					time: testTime,
					assessmentId: parseInt(assessmentID),
					userMessage: currentMessage
				};
				createModuleMutation.mutate(moduleData);
			}
		} else if (!canUseChatbot) {
			// Add warning message if user tries to chat without filling required fields
			const warningMessage = {
				id: Date.now(),
				type: "ai",
				message: "Please fill in the test name and select a time limit before I can help you generate questions.",
				timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
			};
			setChatMessages(prev => [...prev, warningMessage]);
		}
	};

	const handleKeyDown = (e) => {
		if (e.key === 'Enter') {
			handleSendMessage();
		}
	};

	const handleAnswerSelect = (questionId, answerIndex) => {
		const question = generatedQuestions.find(q => q.id === questionId);

		if (question?.type === 'multiple_choice') {
			// Handle multiple choice - toggle selection
			setSelectedAnswers(prev => {
				const currentSelections = prev[questionId] || [];
				const isSelected = currentSelections.includes(answerIndex);

				if (isSelected) {
					// Remove from selection
					return {
						...prev,
						[questionId]: currentSelections.filter(index => index !== answerIndex)
					};
				} else {
					// Add to selection
					return {
						...prev,
						[questionId]: [...currentSelections, answerIndex]
					};
				}
			});
		} else {
			// Handle single choice - replace selection
			setSelectedAnswers(prev => ({
				...prev,
				[questionId]: answerIndex
			}));
		}
	};

	const handleAcceptQuestion = (questionId) => {
		const question = generatedQuestions.find(q => q.id === questionId);
		if (question) {
			// Post question to assessment using mutation
			postQuestionMutation.mutate(question);
		}
	};

	const handleRejectQuestion = (questionId) => {
		const question = generatedQuestions.find(q => q.id === questionId);
		if (question) {
			setRejectedQuestions(prev => [...prev, question]);
			// Remove from generated questions
			setGeneratedQuestions(prev => prev.filter(q => q.id !== questionId));

			// Add rejection message to chat
			const rejectionMessage = {
				id: Date.now(),
				type: "ai",
				message: `Question "${question.question.substring(0, 50)}..." has been rejected.`,
				timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
			};
			setChatMessages(prev => [...prev, rejectionMessage]);
		}
	};

	const toggleChatExpansion = () => {
		setIsChatExpanded(!isChatExpanded);
	};

	const toggleQuestionExpansion = (questionId) => {
		setExpandedQuestions(prev => ({
			...prev,
			[questionId]: !prev[questionId]
		}));
	};

	// Helpers copied to match Questions page behavior
	const removeInlineStyles = (html) => {
		try {
			const parser = new DOMParser();
			const doc = parser.parseFromString(html || "", "text/html");
			const elementsWithStyle = doc.querySelectorAll("[style]");
			elementsWithStyle.forEach((element) => {
				element.removeAttribute("style");
			});
			const strongTags = doc.querySelectorAll("strong");
			strongTags.forEach((tag) => {
				const parent = tag.parentNode;
				while (tag.firstChild) {
					parent.insertBefore(tag.firstChild, tag);
				}
				parent.removeChild(tag);
			});
			const imgTags = doc.querySelectorAll("img");
			imgTags.forEach((img) => {
				img.parentNode.removeChild(img);
			});
			return doc.documentElement.innerHTML;
		} catch (e) {
			return html;
		}
	};

	const previewModule = (id) => {
		const url = `/preview/question/${id}`;
		window.open(url, "_blank");
	};

	const handleRemoveAcceptedQuestion = (id) => {
		// Show confirmation modal (same as Questions.js)
		setQuestionToDelete(id);
		setDeleteModal(true);
	};

	const confirmDeleteQuestion = () => {
		if (!questionToDelete) return;

		setDeleteLoading(true);
		// Call API to delete question from database (same as Questions.js)
		const config = {
			method: "delete",
			maxBodyLength: Infinity,
			url: `/questions/${questionToDelete}/sections/${data?.customQuestion}`,
			headers: {
				accept: "*/*",
			},
		};

		http
			.request(config)
			.then((response) => {
				// Remove from local state after successful API call
				setAcceptedQuestions((prev) => prev.filter((q) => q.id !== questionToDelete));
				console.log("Question deleted successfully");
				setDeleteLoading(false);
				setDeleteModal(false);
				setQuestionToDelete(null);
			})
			.catch((error) => {
				console.error("Error deleting question:", error);
				alert(`Error deleting question: ${error.response?.data?.message || error.message || 'Please try again!'}`);
				setDeleteLoading(false);
				setDeleteModal(false);
				setQuestionToDelete(null);
			});
	};

	const handleEditQuestion = (question, index) => {
		// Same functionality as Questions.js
		setQuestionModal(true);
		setShowQuestion(true);
		setQuestionIndex(index);
	};

	// Close time dropdown on outside click
	useEffect(() => {
		function HandleOutside(event) {
			if (
				TimeRef.current &&
				!TimeRef.current.contains(event.target) &&
				event.target.id !== "time" &&
				!event.target.closest(".TimeClass")
			) {
				setTimeDropdown(false);
			}
		}
		document.addEventListener("mousedown", HandleOutside);
		return () => {
			document.removeEventListener("mousedown", HandleOutside);
		};
	}, [TimeRef]);

	return (
		<div className="bg-white">
			{/* Delete Modal */}
			{deleteModal && (
				<DeleteModal
					setDeleteModal={setDeleteModal}
					onClick={confirmDeleteQuestion}
					header={t("create_test.questions.delete_modal_table.delete_question")}
					icon={DeleteIcon}
					loading={deleteLoading}
					description={t("create_test.questions.delete_modal_table.delete_question_description")}
					buttonText={t("create_test.questions.delete_modal_table.delete_question_button")}
				/>
			)}

			{/* Questions Modal for editing */}
			<QuestionsModal
				questionModal={questionModal}
				setQuestionModal={setQuestionModal}
				showQuestion={showQuestion}
				setQuestionShow={setShowQuestion}
				questionIndex={questionIndex}
				questions={acceptedQuestions}
				setQuestions={setAcceptedQuestions}
				setData={(updatedData) => {
					// Ensure the data structure is compatible with QuestionsModal
					if (setData) {
						setData(updatedData);
					}
				}}
				data={{
					...data,
					customQuestionDetail: acceptedQuestions // Ensure this property exists
				}}
				setQuestionIndex={setQuestionIndex}
				libraryRefetch={() => {}} // Empty function since we don't need library refetch in AI component
			/>

			{/* Main Content */}
			<div className="flex gap-8 p-6">
				{/* Left Section - Custom Test Configuration */}
				<div className={`space-y-6 transition-all duration-300 ease-in-out ${isChatExpanded ? 'flex-1' : 'flex-1'
					}`}>
					{!hasGeneratedQuestions ? (
						<>
							<div className="flex flex-row gap-5 items-center justify-between">
								<h2 className="text-coalColor" style={{ fontFamily: "Archia Semibold" }}>
									Add custom module with Dexta AI
								</h2>

								<button
									onClick={onGoBack}
									className="text-sm text-gray-500 hover:text-black hover transition-colors" style={{ fontFamily: "Silka" }}
								>
									← Go back to tests
								</button>
							</div>
							{/* Test Name and Time Inputs - Side by side */}
							<div className="grid grid-cols-2 gap-4">
								{/* Custom Module Name */}
								<div>
									<div className="flex flex-row">
										<h1 className="font-medium mr-4 lg:mt-0 sm:mt-3" style={{ fontFamily: "Silka" }}>
											{t("create_test.questions.custom_module_name")}
										</h1>
										<div className="group relative flex justify-center lg:mt-0 sm:mt-3">
											<img src={info} className="w-5 h-5 cursor-pointer" />
											<span className="absolute scale-0 right-0 bottom-10 rounded bg-coalColor left-50 p-2 text-xs w-1008 text-white group-hover:scale-100">
												{t("create_test.questions.name_tooltip")}
											</span>
										</div>
									</div>
									<div className="relative w-full border border-1 mt-2 rounded-lg border-[#D3D5D8] focus:border-coalColor focus-within:border-coalColor">
										<input
											type="text"
											value={testName}
											onChange={(e) => setTestName(e.target.value)}
											placeholder={t("create_test.questions.add_name_placeholder")}
											style={{ fontFamily: "Silka" }}
											className="w-full p-3 sm:text-sm xl:text-base 2x:text-lg rounded-lg outline-none bg-[#F6F7F7]"
										/>
									</div>
								</div>

								{/* Custom Module Time */}
								<div>
									<div className="flex flex-row">
										<h1 className="font-medium mr-4" style={{ fontFamily: "Silka" }}>
											{t("create_test.questions.custom_module_time")}
										</h1>
										<div className="group relative flex justify-center">
											<img src={info} className="w-5 h-5 cursor-pointer" />
											<span className="absolute scale-0 right-0 bottom-10 rounded bg-coalColor left-50 p-2 text-xs w-1008 text-white group-hover:scale-100">
												{t("create_test.questions.time_tooltip")}
											</span>
										</div>
									</div>
									<div className="flex flex-col gap-3 relative">
										<button
											id="time"
											className="TimeClass bg-[#F6F7F7] border border-1 border-[#D3D5D8] focus:border-coalColor focus-within:border-coalColor sm:py-3 md:py-0 h-1011 mt-2  focus:outline-[#4A9CB9] rounded-lg text-left inline-flex items-center px-3"
											type="button"
											onClick={() => setTimeDropdown(!timeDropdown)}
											style={{ fontFamily: "Silka" }}
										>
											{testTime && !isNaN(testTime) ? (
												<span className="sm:text-sm xl:text-base 2x:text-lg ">{testTime} {t("create_test.questions.mins")}</span>
											) : (
												<span className="sm:text-sm xl:text-base 2x:text-lg  text-[#999]">{t("create_test.questions.set_max_time")}</span>
											)}
										</button>
										{timeDropdown && (
											<div
												id="education-drop"
												className=" absolute TimeClass z-20 border border-coalColor assessmentClass right-0 top-full h-[12rem] overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full mt-2"
											>
												{TimeData.map((i) => (
													<ul
														key={i.value}
														className="text-sm text-coalColor rounded hover:bg-coalColor hover:text-white cursor-pointer"
														style={{ fontFamily: "Silka" }}
														ref={TimeRef}
														onClick={() => {
															setTestTime(i?.value);
															setTimeDropdown(false);
															setTimeTitle(i?.title);
														}}
													>
														<li>
															<p className="block px-5 py-2 text-xs font-medium">{i.title}</p>
														</li>
													</ul>
												))}
											</div>
										)}
									</div>
								</div>
							</div>

							{/* Selected Questions Table (Copied styling) */}
							<div className="w-full mt-8">
								<h3 className="text-coalColor text-lg font-bold mb-4" style={{ fontFamily: "Archia Semibold" }}>
									Selected Questions
								</h3>
								<div className="relative w-full overflow-x-auto bg-white sm:rounded-lg" id="assessment2">
									<table className="w-full sm:text-xs md:text-sm sm:text-left md:text-left text-white ">
										<thead className="sm:text-xs md:text-lg text-coalColor bg-white" style={{ fontFamily: "Archia Semibold" }}>
											<tr>
												<th scope="col" className="sm:px-3 md:px-6 w-[10%] sm:py-2">{t("create_test.questions.table.headers.id")}</th>
												<th scope="col" className="sm:px-3 md:px-6 w-[40%] sm:py-2">{t("create_test.questions.table.headers.question")}</th>
												<th scope="col" className="sm:px-3 md:px-6 w-[20%] sm:py-2 ">{t("create_test.questions.table.headers.correct_options")}</th>
												<th scope="col" className="sm:px-3 md:px-6 w-[10%] sm:py-2">{t("create_test.questions.table.headers.type")}</th>
												<th scope="col" className="sm:px-3 md:px-6 w-[10%] sm:py-2">{t("create_test.questions.table.headers.status")}</th>
												<th scope="col" className="sm:px-3 md:px-6 w-[10%] sm:py-2">{t("create_test.questions.table.headers.image")}</th>
											</tr>
										</thead>
										<tbody className="rounded-lg">
											{acceptedQuestions && acceptedQuestions.length > 0 ? (
												acceptedQuestions.map((q, index) => {
													// Find correct options (same logic as Questions.js)
													const correctOptions = [];
													q?.options?.forEach((option) => {
														if (option?.isCorrectOption) { // Database format uses isCorrectOption
															correctOptions.push(option);
														}
													});

													return (
														<tr key={q.id || index} className={`bg-white odd:bg-[#F6F7F7] text-black cursor-pointer`} style={{ fontFamily: "Silka" }}>
															<td className="md:px-6 sm:px-2 sm:py-3">{q.id}</td>
															<td className="md:px-6 my-auto sm:px-2 sm:py-3" style={{ maxWidth: "170px", overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }}>
																<div className="my-auto align-center flex" dangerouslySetInnerHTML={{ __html: removeInlineStyles(q.description) }} />
															</td>
															<td className={`md:px-6 sm:px-2 ${q?.type === "Multiple" ? "text-left" : "text-justify"} sm:py-3 flex flex-col my-auto gap-3`}>
																{q?.type === "Multiple" ? (
																	correctOptions?.map((option, optIndex) => (
																		<p key={optIndex} className="my-auto flex items-center">
																			{option.title}
																		</p>
																	))
																) : correctOptions[0]?.title ? (
																	<p className="my-auto flex items-center">
																		{correctOptions[0].title}
																	</p>
																) : (
																	<p className="my-auto flex items-center">-</p>
																)}
															</td>
															<td className="md:px-6 sm:px-2 sm:py-3">{q.type === 'Multiple' ? t("create_test.questions.types.multiple") : t("create_test.questions.types.single")}</td>
															<td className="md:px-6 sm:px-2 lg:text-left sm:text-center sm:py-3">{t("create_test.questions.status.active")}</td>
															<td className="md:px-6 sm:px-2 sm:py-3 lg:text-left sm:text-center">{t("create_test.questions.table.content.no")}</td>
															<td className="md:px-6 sm:px-2 sm:py-3 clickable">
																<div className="my-auto flex flex-row items-center gap-3 w-[8rem]">
																	<MdModeEditOutline
																		className="w-4 h-4 my-auto cursor-pointer hover:text-coalColor transition-colors"
																		onClick={() => handleEditQuestion(q, index)}
																		title="Edit Question"
																	/>
																	<img
																		src={bin}
																		className="w-3 h-4 my-auto flex cursor-pointer hover:opacity-70 transition-opacity"
																		onClick={() => handleRemoveAcceptedQuestion(q.id)}
																		title="Delete Question"
																	/>
																	<div className="relative group">
																		<img
																			src={eye}
																			className="w-5 object-contain h-3 my-auto flex cursor-pointer hover:opacity-70 transition-opacity"
																			onClick={() => previewModule(q.id)}
																			title="Preview Question"
																		/>
																		<div className="tooltipxD right-0 group-hover:block hidden w-20 text-center absolute top-full opacity-0 pointer-events-none text-xs">
																			{t("create_test.questions.preview")}
																		</div>
																	</div>
																</div>
															</td>
														</tr>
													);
												})
											) : (
												<tr>
													<td colSpan="7" className="text-center mt-10">
														<div className="mt-3 mb-5">
															<div className="border border-[#FF5812] py-4 rounded">
																<p className="text-alertRed text-center" style={{ fontFamily: "Silka" }}>
																	{t("create_test.questions.no_custom_questions")}
																</p>
															</div>
														</div>
													</td>
												</tr>
											)}
										</tbody>
									</table>
								</div>
							</div>

							{/* Add Question Button */}
							{/* <div className="flex">
								<div className="ml-auto" style={{ fontFamily: "Silka" }}>
									<CustomButton
										label="Add Custom Question"
										bgColor="#C0FF06"
										textColor="#252E3A"
										paddingY="0.65rem"
										textSize="text-base"
										borderCustom="border border-black"
										hoverBgColor="#252E3A"
										hoverTextColor="#C0FF06"
										paddingx="px-6"
										onClickButton={onAddQuestion}
									/>
								</div>
							</div> */}
						</>
					) : (
						<>
							{/* AI Generated Questions Interface */}
							<h1 className="text-gray-coalColor" style={{ fontFamily: "Archia Semibold" }}>
								Custom Questions with AI
							</h1>
							<p className="text-lg text-gray-600" style={{ fontFamily: "Silka" }}>
								Review AI-Generated Questions
							</p>

							{/* Green Banner */}
							<div className="bg-[#C0FF06] border border-black rounded-lg p-4 flex items-center gap-3">
								<FaInfoCircle className="text-black text-lg" />
								<span className="text-black font-medium" style={{ fontFamily: "Silka" }}>
									The Dexta AI Have Generated {generatedQuestions.length} Questions
								</span>
							</div>

							{/* Generated Questions List */}
							<div className="space-y-3 max-h-96 overflow-y-auto">
								{generatedQuestions.map((question, index) => {
									const isExpanded = expandedQuestions[question.id];
									return (
										<div key={question.id} className="border border-gray-200 rounded-lg bg-white overflow-hidden">
											{/* Question Header - Always Visible */}
											<div
												className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
												onClick={() => toggleQuestionExpansion(question.id)}
											>
												<div className="flex items-center gap-3 flex-1">
													<div className="flex items-center gap-2">
														<span className="text-sm font-medium text-coalColor" style={{ fontFamily: "Archia Semibold" }}>
															Q{index + 1}
														</span>
														<svg
															className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
															fill="none"
															stroke="currentColor"
															viewBox="0 0 24 24"
														>
															<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
														</svg>
													</div>
													<p className="text-sm text-coalColor truncate flex-1" style={{ fontFamily: "Silka" }}>
														{question.question}
													</p>
												</div>

												<div className="flex items-center gap-2 ml-4">
													{/* Quick Action Buttons */}
													<div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
														<button
															onClick={() => handleAcceptQuestion(question.id)}
															className="p-1.5 text-green-600 hover:bg-green-50 rounded-md transition-colors"
															title="Accept Question"
														>
															<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
															</svg>
														</button>
														<button
															onClick={() => handleRejectQuestion(question.id)}
															className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors"
															title="Reject Question"
														>
															<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
															</svg>
														</button>
													</div>
												</div>
											</div>

											{/* Expanded Content */}
											{isExpanded && (
												<div className="px-4 pb-4 border-t border-gray-100">
													{/* Full Question Text */}
													<div className="mb-4 pt-4">
														<p className="text-coalColor font-medium" style={{ fontFamily: "Silka", fontSize: "16px" }}>
															{question.question}
														</p>
													</div>

													{/* Instruction Text */}
													<div className="mb-4">
														<p className="text-coalColor text-sm font-bold" style={{ fontFamily: "Archia Bold" }}>
															{question.type === 'multiple_choice' ? 'Select all that apply:' : 'Select only one:'}
														</p>
													</div>

													{/* Options */}
													<div className="space-y-3">
														{question.options.map((option, optionIndex) => {
															const isCorrect = option.isCorrect;
															// Show correct answers as selected by default
															const isSelected = question.type === 'multiple_choice'
																? (selectedAnswers[question.id] !== undefined
																	? (selectedAnswers[question.id] || []).includes(optionIndex)
																	: isCorrect) // Default to correct if no selection made
																: (selectedAnswers[question.id] !== undefined
																	? selectedAnswers[question.id] === optionIndex
																	: isCorrect); // Default to correct if no selection made

															return (
																<div key={optionIndex} className={`px-4 py-3 rounded-xl transition-all ${
																	isCorrect ? 'bg-[#C0FF06] border border-black' : 'bg-gray-50 border-2 border-gray-200'
																}`}>
																	<div className="flex justify-between items-center">
																		<div className="flex items-center gap-3">
																			<div className="inline-flex items-center">
																				<label className="relative flex cursor-pointer items-center rounded-full p-3">
																					<input
																						type={question.type === 'multiple_choice' ? 'checkbox' : 'radio'}
																						name={`question-${question.id}`}
																						checked={isSelected}
																						onChange={() => handleAnswerSelect(question.id, optionIndex)}
																						className={question.type === 'multiple_choice'
																							? "peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-gray-600 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor hover:before:opacity-10"
																							: "peer relative h-5 w-5 cursor-pointer appearance-none rounded-full border border-gray-600 text-coalColor transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:opacity-0 before:transition-opacity checked:border-coalColor checked:before:bg-black hover:before:opacity-10"
																						}
																					/>
																					<div className="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-coalColor opacity-0 transition-opacity peer-checked:opacity-100">
																						{question.type === 'multiple_choice' ? (
																							<svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor" stroke="currentColor" strokeWidth="1">
																								<path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
																							</svg>
																						) : (
																							<svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 16 16" fill="currentColor">
																								<circle cx="8" cy="8" r="8"></circle>
																							</svg>
																						)}
																					</div>
																				</label>
																				<label className={`mt-px cursor-pointer select-none text-sm font-medium ${isCorrect ? 'text-coalColor' : 'text-coalColor'}`} style={{ fontFamily: "Silka" }}>
																					{isCorrect && <span className="text-coalColor mr-2">✓</span>}
																					{option.text}
																				</label>
																			</div>
																		</div>
																	</div>
																</div>
															);
														})}
													</div>
												</div>
											)}
										</div>
									);
								})}
							</div>

							{/* Back to Configuration Button */}
							<div className="flex justify-center mt-6">
								<CustomButton
									label="Back to Configuration"
									bgColor="white"
									textColor="#252E3A"
									paddingY="0.5rem"
									textSize="text-sm"
									borderCustom="border border-black"
									hoverBgColor="#252E3A"
									hoverTextColor="white"
									paddingx="px-6"
									onClickButton={() => setHasGeneratedQuestions(false)}
								/>
							</div>
						</>
					)}
				</div>

				{/* Right Section - Dexta AI Chat Interface */}
				<div className={`flex flex-col h-[600px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-lg border border-gray-200 transition-all duration-300 ease-in-out ${isChatExpanded ? 'w-[35%]' : 'w-[20%]'
					}`}>
					{/* Header - Fixed at top */}
					<div className="flex items-center justify-between flex-shrink-0 px-6 py-3 pb-4 bg-gradient-to-r from-coalColor to-gray-800 rounded-t-xl">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 bg-gradient-to-br from-[#C0FF06] to-[#A0D000] rounded-full flex items-center justify-center shadow-lg">
								<FaRobot className="text-coalColor text-lg font-bold" />
							</div>
							<div>
								<h2 className="text-white font-bold text-lg" style={{ fontFamily: "Archia Semibold" }}>
									Dexta AI
								</h2>
								<p className="text-xs text-gray-300" style={{ fontFamily: "Silka" }}>
									AI Assistant
								</p>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<button
								onClick={toggleChatExpansion}
								className="p-2 text-gray-300 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-lg"
								title={isChatExpanded ? "Collapse chat" : "Expand chat"}
							>
								{isChatExpanded ? (
									<FaCompressArrowsAlt className="text-sm" />
								) : (
									<FaExpandArrowsAlt className="text-sm" />
								)}
							</button>
						</div>
					</div>

					{/* Chat Messages - Takes remaining space when expanded */}
					{isChatExpanded && (
						<div ref={chatMessagesRef} className="flex-1 overflow-y-auto min-h-0 px-6 py-4 bg-gradient-to-b from-gray-50 to-white">
							<div className="space-y-6">
								{chatMessages.map((msg) => (
									<div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
										<div className={`flex ${msg.type === 'user' ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-md`}>
											{/* Message Content */}
											<div className="flex flex-col" style={{ fontFamily: "Silka" }}>
												<div className={`px-4 py-3 rounded-2xl shadow-sm ${msg.type === 'ai'
													? 'bg-white text-gray-800 border border-gray-100 shadow-md'
													: 'bg-gradient-to-r from-coalColor to-gray-700 text-white shadow-md'
													}`}>
													{msg.isLoading ? (
														<div className="flex items-center gap-2">
															<div className="flex space-x-1">
																<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
																<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
																<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
															</div>
															<p className="text-sm leading-relaxed">{msg.message}</p>
														</div>
													) : msg.clarificationQuestions ? (
														<div className="space-y-3">
															<p className="text-sm leading-relaxed">{msg.message}</p>
															<div className="space-y-2">
																{msg.clarificationQuestions.map((question, index) => (
																	<button
																		key={index}
																		className="w-full text-left p-2 cursor-default text-sm bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg transition-colors duration-200 flex items-center gap-2"
																	>
																		<span className="text-gray-600">→</span>
																		<span>{question}</span>
																	</button>
																))}
															</div>
														</div>
													) : (
														<p className="text-sm leading-relaxed">{msg.message}</p>
													)}
												</div>
												<div className={`text-xs text-gray-500 mt-2 ${msg.type === 'ai' ? 'text-left' : 'text-right'
													}`}>
													{msg.timestamp}
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Chat Messages - Compact view for collapsed state */}
					{!isChatExpanded && (
						<div ref={chatMessagesCompactRef} className="flex-1 overflow-y-auto min-h-0 px-4 py-3 bg-gradient-to-b from-gray-50 to-white">
							<div className="space-y-3">
								{chatMessages.map((msg) => (
									<div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
										<div className={`flex ${msg.type === 'user' ? 'flex-row-reverse' : 'flex-row'} items-start gap-2 max-w-[200px]`}>
											{/* Message Content */}
											<div className="flex flex-col" style={{ fontFamily: "Silka" }}>
												<div className={`px-3 py-2 rounded-xl shadow-sm ${msg.type === 'ai'
													? 'bg-white text-gray-800 border border-gray-100'
													: 'bg-gradient-to-r from-coalColor to-gray-700 text-white'
													}`}>
													{msg.isLoading ? (
														<div className="flex items-center gap-2">
															<div className="flex space-x-1">
																<div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"></div>
																<div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
																<div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
															</div>
															<p className="text-sm leading-tight break-words">{msg.message}</p>
														</div>
													) : msg.clarificationQuestions ? (
														<div className="space-y-2">
															<p className="text-sm leading-tight break-words">{msg.message}</p>
															<div className="space-y-1">
																{msg.clarificationQuestions.map((question, index) => (
																	<button
																		key={index}
																		className="w-full cursor-default text-left p-1.5 text-xs bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded transition-colors duration-200 flex items-start gap-1"
																	>
																		<span className="text-gray-600 text-xs flex-shrink-0 mt-0.5">→</span>
																		<span className="break-words leading-tight">{question}</span>
																	</button>
																))}
															</div>
														</div>
													) : (
														<p className="text-sm leading-tight break-words">{msg.message}</p>
													)}
												</div>
												<div className={`text-xs text-gray-500 mt-1 ${msg.type === 'ai' ? 'text-left' : 'text-right'
													}`}>
													{msg.timestamp}
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Chat Input - Fixed at bottom */}
					<div className="flex-shrink-0 p-6 pt-4 bg-white border-t border-gray-200 rounded-b-xl">
						{/* Validation Message */}
						{!canUseChatbot && (
							<div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
								Fill test name and time to start chatting
							</div>
						)}

						<div className="relative">
							<input
								type="text"
								value={chatMessage}
								onChange={(e) => setChatMessage(e.target.value)}
								onKeyDown={handleKeyDown}
								placeholder={isChatExpanded ? "Type your message here..." : "Chat..."}
								className={`w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#C0FF06] focus:border-transparent transition-all duration-300 shadow-sm ${isChatExpanded ? 'text-base' : 'text-sm'
									} ${!canUseChatbot ? 'bg-gray-100 cursor-not-allowed' : ''}`}
								style={{ fontFamily: "Silka" }}
								disabled={!canUseChatbot || createModuleMutation.isPending || isGeneratingQuestions || isContinuingConversation}
							/>
							<button
								onClick={handleSendMessage}
								className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed ${canUseChatbot && !createModuleMutation.isPending && !isGeneratingQuestions && !isContinuingConversation
									? 'bg-gradient-to-r from-coalColor to-gray-700 text-white hover:from-gray-700 hover:to-coalColor'
									: 'bg-gray-300 text-gray-500 cursor-not-allowed'
									}`}
								disabled={!canUseChatbot || createModuleMutation.isPending || isGeneratingQuestions || isContinuingConversation}
								title={!canUseChatbot ? 'Fill test name and time first' : (createModuleMutation.isPending || isGeneratingQuestions || isContinuingConversation) ? 'Processing...' : 'Send message'}
							>
								{(createModuleMutation.isPending || isGeneratingQuestions || isContinuingConversation) ? (
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
								) : (
									<FaArrowRight className={`transition-all duration-300 ${canUseChatbot && !createModuleMutation.isPending && !generateQuestionsMutation.isPending ? 'text-white' : 'text-gray-400'
										}`} />
								)}
							</button>
						</div>

						{/* Error Message */}
						{chatError && (
							<div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-800">
								{chatError}
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default CreateCustomTestAI; 