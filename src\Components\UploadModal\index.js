import React, { useState } from "react";
import http from "../../http";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { updateCompany } from "../../Pages/Profile/Settings/hooks/updateCompany";
import Loader from "react-loader-spinner";
import { IoCheckmarkDoneCircleOutline } from "react-icons/io5";
import { FaUpload, FaVideo } from "react-icons/fa";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";

const UploadModal = ({
  files,
  setFiles,
  companyID,
  uploadModal,
  setUploadModal,
}) => {
  const { t } = useTranslation();
  const [isDragging, setIsDragging] = useState(false);
  const userID = parseInt(localStorage.getItem("CP-USER-ID"));
  const queryClient = useQueryClient();
  const [fakeProgress, setFakeProgress] = useState(0); // Fake progress bar
  const [uploadProgress, setUploadProgress] = useState(0); // Real upload progress
  const [uploadLoading, setUploadLoading] = useState(false);
  const [successStatus, setSuccessStatus] = useState(false);

  const handleFileSelect = (file) => {
    if (!file) return;

    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    if (file.size > maxSize) {
      toast.error(t("file_size_exceeds"), {
        toastId: "copy-error",
      });
      return;
    }

    setFiles(file);
    setFakeProgress(0);
    setUploadProgress(0);
    setSuccessStatus(false);

    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      if (progress <= 100) {
        setFakeProgress(progress);
      } else {
        clearInterval(interval);
      }
    }, 100);
  };

  const handleSubmitVideo = async (file) => {
    setUploadLoading(true);
    setFakeProgress(100);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileType", "user");

    try {
      const response = await http.post("/upload-video", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(percentCompleted);
        },
      });

      setUploadLoading(false);
      setSuccessStatus(true);
      setTimeout(() => {
        setFiles(null);
        setFakeProgress(0);
        setUploadProgress(0);
        setUploadModal(false);
      }, 2000);

      let data = {
        data1: JSON.stringify({
          user: userID,
          companyVideo: response?.data?.data,
        }),
        data2: {
          companyID: companyID,
        },
      };

      companyMutate(data);
    } catch (error) {
      setUploadLoading(false);
      console.error("Upload Error:", error);
      toast.error(t("upload_failed"), {
        toastId: "upload-error",
      });
    }
  };

  const { mutate: companyMutate } = useMutation(updateCompany, {
    onSuccess: () => {
      queryClient.invalidateQueries("/company");
      toast.success(t("video_uploaded_success_toast"), {
        toastId: "upload-success",
      });
    },
  });

  return (
    <>
      {uploadModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70 p-4">
          <div className="w-full max-w-[640px] bg-white rounded-lg shadow-xl">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2
                    className="text-xl font-bold text-coalColor flex items-center"
                    style={{ fontFamily: "Archia Semibold" }}
                  >
                    {successStatus ? (
                      <span className="flex items-center gap-2">
                        <IoCheckmarkDoneCircleOutline className="text-green-600 text-2xl" />
                        {t("video_uploaded_successfully")}
                      </span>
                    ) : (
                      <span className="flex items-center gap-2">
                        <FaVideo className="text-coalColor mr-2 sm:w-8 sm:h-8 md:w-5 md:h-5" />
                        {t("upload_company_introduction_video")}
                      </span>
                    )}
                  </h2>
                  <p
                    className="mt-2 text-sm text-gray-600"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("video_shown_before_test")}
                  </p>
                </div>
                <button
                  onClick={() => {
                    setUploadModal(false);
                    setSuccessStatus(false);
                    setFiles(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Close"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Upload Area */}
              {!files ? (
                <div
                  className={`relative border-2 border-dashed rounded-lg p-8 text-center
                    ${
                      isDragging
                        ? "border-coalColor bg-gray-50"
                        : "border-[#D3D5D8] bg-gray-50"
                    }
                    transition-colors duration-200 hover:border-coalColor`}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setIsDragging(true);
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    setIsDragging(false);
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    setIsDragging(false);
                    handleFileSelect(e.dataTransfer.files[0]);
                  }}
                >
                  <input
                    type="file"
                    accept="video/mp4,video/mov,video/avi"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onChange={(e) => handleFileSelect(e.target.files[0])}
                  />
                  <div className="py-6">
                    <div className="bg-gray-100 p-4 rounded-full inline-flex mb-4">
                      <FaUpload className="text-coalColor text-3xl" />
                    </div>
                    <p
                      className="text-gray-700 font-medium mb-2"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("click_upload_drag_drop")}
                    </p>
                    <p
                      className="text-sm text-gray-500 mb-4"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("supported_formats")}
                    </p>
                    <button
                      className="inline-flex items-center px-4 py-2 bg-coalColor text-white rounded-md hover:bg-primaryGreen hover:text-black transition-colors duration-200"
                      style={{ fontFamily: "Silka" }}
                    >
                      <FaUpload className="mr-2" />
                      {t("select_video")}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="border rounded-lg p-5 bg-gray-50 border-[#D3D5D8]">
                  <div className="flex justify-between items-center mb-4 gap-3">
                    <div className="flex items-center min-w-0 flex-1">
                      <div className="bg-coalColor bg-opacity-10 p-2 rounded-md mr-3 flex-shrink-0">
                        <FaVideo className="text-coalColor text-xl" />
                      </div>
                      <div className="min-w-0 flex-1 overflow-hidden">
                        <p
                          className="font-medium text-gray-900 break-words overflow-wrap-anywhere whitespace-normal"
                          style={{ fontFamily: "Silka" }}
                        >
                          {files.name}
                        </p>
                        <p
                          className="text-sm text-gray-500 break-words overflow-wrap-anywhere"
                          style={{ fontFamily: "Silka" }}
                        >
                          {Math.round(files.size / (1024 * 1024))} MB
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setFiles(null)}
                      className="text-gray-400 hover:text-gray-600 p-1 hover:bg-gray-100 rounded-full"
                      aria-label="Remove file"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Progress Bars */}
                  {fakeProgress > 0 && fakeProgress < 100 && (
                    <div className="mt-3">
                      <div className="flex justify-between text-xs mb-1">
                        <span style={{ fontFamily: "Silka" }}>
                          {t("processing_video")}
                        </span>
                        <span style={{ fontFamily: "Silka" }}>
                          {fakeProgress}%
                        </span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-coalColor transition-all duration-200"
                          style={{ width: `${fakeProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {fakeProgress === 100 && uploadProgress > 0 && (
                    <div className="mt-3">
                      <div className="flex justify-between text-xs mb-1">
                        <span style={{ fontFamily: "Silka" }}>
                          {t("uploading_video")}
                        </span>
                        <span style={{ fontFamily: "Silka" }}>
                          {uploadProgress}%
                        </span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500 transition-all duration-200"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Footer */}
              <div className="flex justify-end mt-6 sm:text-sm md:text-base space-x-3">
                <button
                  onClick={() => {
                    setUploadModal(false);
                    setSuccessStatus(false);
                    setFiles(null);
                  }}
                  className="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors duration-200"
                  style={{ fontFamily: "Silka" }}
                >
                  {t("cancel")}
                </button>

                {!successStatus && (
                  <button
                    onClick={() => handleSubmitVideo(files)}
                    disabled={!files || fakeProgress !== 100}
                    className={`px-4 py-2 font-medium rounded-md transition-colors duration-200
                      ${
                        files && fakeProgress === 100
                          ? "bg-coalColor hover:bg-primaryGreen text-white hover:text-black"
                          : "bg-[#D3D5D8] text-[#7C8289] cursor-not-allowed"
                      }`}
                    style={{ fontFamily: "Silka" }}
                  >
                    {uploadLoading ? (
                      <span className="flex items-center">
                        <Loader
                          type="Oval"
                          color="white"
                          height={20}
                          width={20}
                        />
                        <span className="ml-2">{t("uploading")}</span>
                      </span>
                    ) : (
                      t("upload_video_button")
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UploadModal;
