import React, { useState } from "react";
import CollapseImage from "../../../Assets/collapseSidebar.png";
import { Link } from "react-router-dom";
import ChatSidebar from "../ChatSidebar";
import Logo from "../../../Dexta_assets/logodexta2.png";
import withRouter from "../../../Components/Common/withRouter";
import { useQuery } from "@tanstack/react-query";
import { getUserData } from "../../Profile/MyAssessments/Assessments-main/hooks/getUserData";
import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import Divider from "@mui/material/Divider";
import Settings from "@mui/icons-material/Settings";
import Logout from "@mui/icons-material/Logout";
import http from "../../../http";
import { getChats } from "../hooks/getChats";
const ChatLayout = (props) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const userID = localStorage.getItem("CP-USER-ID");
  const { router } = props;

  //#region Fetching user data
  const { data, isLoading } = useQuery(["user", userID], () =>
    getUserData(userID)
  );
  //#endregion

  //#region Fetching chats data
  const { data: chatData } = useQuery(["chats"], () => getChats());
  //#endregion

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Handle profile menu
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Sidebar */}
      <ChatSidebar
        isOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        setSidebarOpen={setSidebarOpen}
        chats={chatData}
      />

      {/* Header */}
      <nav className="bg-white fixed w-full z-40 top-0 left-0 border-b border-gray-200">
        <div className="lg:container flex items-center justify-between xl:px-2 2xl:px-0 sm:px-4 mx-auto py-4">
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="text-gray-700 hover:text-gray-900 mr-4"
            >
              <img src={CollapseImage} alt="collapse" className="h-10 w-10" />
            </button>
            {!sidebarOpen && (
              <Link to="/dashboard" className="flex items-center">
                <img
                  src={Logo}
                  className="h-[50px] w-[190px] object-fill"
                  alt="Dexta Logo"
                />
              </Link>
            )}
          </div>

          <div className="flex md:order-2 relative">
            {/* User profile dropdown */}
            <div className="sm:hidden md:block">
              <React.Fragment>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    textAlign: "center",
                  }}
                >
                  <div
                    onClick={handleClick}
                    aria-controls={open ? "account-menu" : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? "true" : undefined}
                    className="flex flex-row cursor-pointer"
                  >
                    <div className="flex flex-row">
                      <button
                        type="button"
                        className="flex text-sm sm:hidden md:block rounded-full"
                      >
                        <span className="sr-only">Open user menu</span>
                        {!isLoading && data?.profilePhoto && (
                          <img
                            className="w-11 h-11 rounded-full border-primaryGreen border object-cover"
                            src={data?.profilePhoto}
                            alt=""
                          />
                        )}
                      </button>
                      {!isLoading && data?.firstName && (
                        <h1
                          className="my-auto ml-4 text-[13px] sm:hidden md:block"
                          style={{ fontFamily: "Archia Semibold" }}
                        >
                          Hello {data?.firstName}
                        </h1>
                      )}
                    </div>
                    <svg
                      className="w-2.5 h-2.5 ml-2 my-auto"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 10 6"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="m1 1 4 4 4-4"
                      />
                    </svg>
                  </div>
                </Box>
                <Menu
                  anchorEl={anchorEl}
                  id="account-menu"
                  open={open}
                  onClose={handleClose}
                  onClick={handleClose}
                  PaperProps={{
                    elevation: 0,
                    sx: {
                      overflow: "visible",
                      filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                      mt: 1.5,
                      "& .MuiAvatar-root": {
                        width: 32,
                        height: 32,
                        ml: -0.5,
                        mr: 1,
                      },
                      "&:before": {
                        content: '""',
                        display: "block",
                        position: "absolute",
                        top: 0,
                        right: 14,
                        width: 10,
                        height: 10,
                        bgcolor: "background.paper",
                        transform: "translateY(-50%) rotate(45deg)",
                        zIndex: 0,
                      },
                    },
                  }}
                  transformOrigin={{ horizontal: "right", vertical: "top" }}
                  anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                >
                  {!isLoading && data && (
                    <MenuItem>
                      <Avatar src={data?.profilePhoto} />
                      <div className="flex flex-col">
                        <div>
                          {data?.firstName} {data?.lastName}
                        </div>
                        <div className="text-xs">{data?.email}</div>
                      </div>
                    </MenuItem>
                  )}
                  <Divider />
                  <MenuItem onClick={() => router.navigate("/settings")}>
                    <ListItemIcon>
                      <Settings fontSize="small" />
                    </ListItemIcon>
                    Settings
                  </MenuItem>
                  <MenuItem
                    onClick={async () => {
                      handleClose();
                      try {
                        await http.post(`/auth/logout`);
                      } catch (error) {
                        console.error("Logout error:", error);
                      } finally {
                        localStorage.removeItem("CP-USER-TOKEN");
                        localStorage.removeItem("CP-USER-ID");
                        localStorage.removeItem("page");
                        window.location.href = "/login";
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Logout fontSize="small" />
                    </ListItemIcon>
                    Logout
                  </MenuItem>
                </Menu>
              </React.Fragment>
            </div>
          </div>
        </div>
      </nav>

      {/* Main content */}
      <div className="flex-1 overflow-hidden mt-[76px]">{props.children}</div>
    </div>
  );
};

export default withRouter(ChatLayout);
