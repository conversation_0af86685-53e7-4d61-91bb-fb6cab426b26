import React from "react";
import King from "../../../../Assets/preee.png";
import Default from "../../../../Assets/default-img.png";
import FileInput from "../../../../Components/FileInput/FileInput";
import { useTranslation } from "react-i18next";

const ImageContainer = ({
  premiumOpen,
  setPremiumOpen,
  maindiv,
  setMainDiv,
  user_package_check,
  companyAvatar,
  imgAfterCrop,
  setImage,
  onImageSelected,
  hex2,
  hex,
}) => {
  const { t } = useTranslation();
  return (
    <>
      <div
        className="border border-[#D3D5D8] relative xl:h-[280px] 2xl:h-[320px] rounded-lg"
        onClick={() => {
          if (user_package_check === "free") {
            setPremiumOpen(true);
          } else {
            setMainDiv(true);
          }
        }}
      >
        <div className="p-2">
          {user_package_check === "free" && (
            <div className="absolute w-7 right-1">
              <img src={King} className="w-5 h-5" />
            </div>
          )}
        </div>
        <div className="mt-2 sm:pt-3 md:pt-[5rem]">
          {imgAfterCrop === null && companyAvatar === "" ? (
            <img src={Default} className="w-[140px] h-[180px] mx-auto" />
          ) : (
            <img
              src={imgAfterCrop}
              className="mx-auto object-contain"
              style={{
                maxWidth: "320px",
                maxHeight: "90px",
                width: "auto",
                height: "auto",
              }}
            />
          )}
          <FileInput
            setImage={setImage}
            maindiv={maindiv}
            setMainDiv={setMainDiv}
            onImageSelected={onImageSelected}
            user_package_check={user_package_check}
          />
        </div>
      </div>
      <p
        className="text-sm text-[#252E3A] mt-5"
        style={{ fontFamily: "Silka Light" }}
      >
        {t("color_palette_description")}
      </p>
      <div className="flex sm:flex-col md:flex-row mt-5">
        <div
          className={`sm:px-2 md:px-4 py-3 md:text-base rounded-md sm:w-full md:w-1/3 text-center font-bold`}
          style={{
            background: hex,
            borderColor: hex2,
            borderWidth: "1px",
            fontFamily: "Silka",
            color: hex2,
          }}
        >
          {t("button")}
        </div>
        <div
          className={`sm:px-2 md:px-4 py-3 sm:text-sm md:text-sm 2xl:text-base rounded-md w-1/3 text-center`}
          style={{
            background: "none",
            borderColor: "white",
            borderWidth: "1px",
            fontFamily: "Silka",
            whiteSpace: "nowrap",
          }}
        >
          <span style={{ color: hex2 }} className="font-bold">
            {t("text_colour")}
          </span>{" "}
          {t("in_comparison_to_black")}
        </div>
      </div>
    </>
  );
};

export default ImageContainer;
