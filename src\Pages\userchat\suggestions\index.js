import React from "react";
import { BiTime } from "react-icons/bi";

const Suggestions = () => {
  const suggestionCards = [
    {
      title: "Private Equity Analyst",
      level: "Entry level",
      time: "10 mins",
      description:
        "Designed to assess foundational understanding and knowledge of Agile methodolog...",
    },
    {
      title: "Valuation Techniques",
      level: "Mid level",
      time: "10 mins",
      description:
        "Designed to assess foundational understanding and knowledge of Agile methodolog...",
    },
    {
      title: "M&A Processes",
      level: "Senior level",
      time: "10 mins",
      description:
        "Designed to assess foundational understanding and knowledge of Agile methodolog...",
    },
    {
      title: "Negotiation Skills",
      level: "Basic",
      time: "10 mins",
      description:
        "Designed to assess foundational understanding and knowledge of Agile methodolog...",
    },
  ];
  return (
    <div className="mt-8">
      {/* <h2
        className="text-xl font-semibold mb-4"
        style={{ fontFamily: "Archia Semibold" }}
      >
        Suggestions:
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {suggestionCards.map((card, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm"
          >
            <div className="p-5">
              <div className="flex justify-between items-center mb-4">
                <h3
                  className="text-lg font-semibold"
                  style={{ fontFamily: "Archia Semibold" }}
                >
                  {card.title}
                </h3>
                <span
                  className={`text-xs px-3 py-1 rounded-full border ${
                    card.level === "Entry level"
                      ? " border-[#0B5B23]"
                      : card.level === "Mid level"
                      ? "border-[#FFB500]"
                      : card.level === "Senior level"
                      ? "border-[#FF5812]"
                      : "border-coalColor"
                  }`}
                  style={{ fontFamily: "Silka" }}
                >
                  {card.level}
                </span>
              </div>

              <div className="flex items-center text-sm text-gray-500 mb-4">
                <BiTime className="mr-1" />
                <span style={{ fontFamily: "Silka" }}>{card.time}</span>
              </div>

              <p
                className="text-sm text-gray-600 mb-4"
                style={{ fontFamily: "Silka" }}
              >
                {card.description}
              </p>

              <button
                className="w-full bg-primaryGreen border border-coalColor text-coalColor font-medium py-3 rounded-md hover:bg-coalColor hover:text-primaryGreen transition-colors"
                style={{ fontFamily: "Silka" }}
              >
                Details
              </button>
            </div>
          </div>
        ))}
      </div> */}
    </div>
  );
};

export default Suggestions;
