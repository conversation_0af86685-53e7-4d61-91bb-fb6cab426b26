import React, { useState } from "react";
import { Link } from "react-router-dom";
import chatIcon from "../../Assets/chaticon.png";
const ChatButton = () => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Link
      to="/chat"
      target="_blank"
      rel="noopener noreferrer"
      className="fixed bottom-6 right-6 z-50 flex items-center"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isHovered && (
        <div
          className="mr-3 bg-white rounded-lg py-2 px-4 shadow-md text-base"
          style={{ fontFamily: "Silka" }}
        >
          <p>Hi! A question? I'm here to help you in creating test</p>
        </div>
      )}
      <div className="bg-primaryGreen rounded-full p-4 shadow-lg hover:bg-lime-500 transition-colors">
        <img
          src={chatIcon}
          alt="chaticon"
          className="text-2xl w-8 h-8 object-contain text-black"
        />
      </div>
    </Link>
  );
};

export default ChatButton;
