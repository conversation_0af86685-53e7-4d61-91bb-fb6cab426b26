//#region Steps related to navbar
export const getSteps = (t) => [
  {
    title: t("navbar.welcomeTitle"),
    intro: (
      <div style={{ fontFamily: "Silka" }}>
        <p>{t("navbar.welcomeIntro")}</p>
      </div>
    ),
  },
  {
    title: t("navbar.myTestsTitle"),
    element: "#tab1",
    intro: <p>{t("navbar.myTestsIntro")}</p>,
  },
];

export const getStepsCandidate = (t) => [
  {
    title: t("navbar.myCandidatesTitle"),
    element: "#tab2",
    intro: (
      <div>
        <p>{t("navbar.myCandidatesIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("navbar.myCandidatesList1")}</li>
          <li>{t("navbar.myCandidatesList2")}</li>
          <li>{t("navbar.myCandidatesList3")}</li>
        </ul>
      </div>
    ),
  },
];

export const getStepsSettings = (t) => [
  {
    title: t("navbar.settingsTitle"),
    element: "#tab3",
    intro: (
      <div>
        <p>{t("navbar.settingsIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("navbar.settingsList1")}</li>
          <li>{t("navbar.settingsList2")}</li>
          <li>{t("navbar.settingsList3")}</li>
          <li>{t("navbar.settingsList4")}</li>
        </ul>
      </div>
    ),
  },
];

export const getStepsTests = (t) => [
  {
    title: t("navbar.modulesTitle"),
    element: "#tab4",
    intro: (
      <div>
        <p>{t("navbar.modulesIntro")}</p>
        <p style={{ marginTop: "8px" }}>{t("navbar.modulesIntro2")}</p>
        <ul className="list-disc px-4">
          <li>{t("navbar.modulesList1")}</li>
          <li>{t("navbar.modulesList2")}</li>
          <li>{t("navbar.modulesList3")}</li>
          <li>{t("navbar.modulesList4")}</li>
        </ul>
      </div>
    ),
  },
];
//#endregion
