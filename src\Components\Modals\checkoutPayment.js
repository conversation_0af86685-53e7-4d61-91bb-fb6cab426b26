import React, { useState } from "react";
import {
  useStripe,
  useElements,
  CardNumberElement,
  CardCvcElement,
  CardExpiryElement,
} from "@stripe/react-stripe-js";
import http from "../../http";
import Loader from "react-loader-spinner";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { attachCard } from "../hooks/attachCard";
import Cardimg from "../../Assets/blackcard.png";
import calendar from "../../Assets/calend.png";
import cvc from "../../Assets/cvc.png";
import { useSelector } from "react-redux";
import { CiCreditCard1 } from "react-icons/ci";
import { setPackageData } from "../../redux/reducers/packageData/packageDataSlice";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { addonsUpgrade } from "../hooks/addonsUpgrade";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import { setFreeToFalse } from "../../redux/reducers/FreeUser/FreeUserSlice";
import { useTranslation } from "react-i18next";
//#region styling of card input fields
const inputStyle = {
  iconColor: "#c4f0ff",
  backgroundColor: "#fff",
  color: "#000",
  fontSize: "16px",
  fontSmoothing: "antialiased",
  ":-webkit-autofill": {
    color: "#000",
  },
  "::placeholder": {
    color: "#6B6B6B",
  },
};
//#endregion

const CheckoutPayment = ({
  paymentDone,
  setPaymentDone,
  loading,
  setIsloading,
  setPaymentOpen,
  refetch,
  shouldUpdate,
  setShouldUpdate,
  subscriptionID,
  addOnId = null,
  addOnPriceIds = null,
  currency = null,
  freeCheck = null,
  onlyAddonPurchase = null,
  setOnlyAddonPurchase = null,
  addonPrice = null,
  mycouponprice = null,
  mycouponcode = null,
  refetchCoupon = null,
  setMyCouponCode = null,
  setMyCouponPrice = null,
  setUserData = null,
  setImmediateUpgrade = null,
  discountedPackages = null,
}) => {
  const dispatch = useDispatch();
  const stripe = useStripe();
  const elements = useElements();
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const queryClient = useQueryClient();
  const userID = localStorage.getItem("CP-USER-ID");
  const user_plan = useSelector((state) => state.planDetails.setPlanDetail);
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState("");
  const [error, setError] = useState(false);
  const [errors, setErrors] = useState({
    cardNumber: "",
    expiry: "",
    cvc: "",
    general: "",
  });
  const { t } = useTranslation();

  console.log(user_plan, "user_plan");
  //#region create subscription
  const createSubscription = async (e) => {
    e.preventDefault();
    try {
      setIsloading(true);

      // Initialize a flag to check for errors
      let hasErrors = false;

      // Handling card number validation
      const cardElement = elements.getElement(CardNumberElement);
      if (cardElement._complete === false) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          cardNumber: t("plans.invalid_card_number"),
        }));
        hasErrors = true;
      } else {
        setErrors((prevErrors) => ({ ...prevErrors, cardNumber: "" }));
      }

      // Handling expiry date validation
      const expiryElement = elements.getElement(CardExpiryElement);
      if (expiryElement._complete === false) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          expiry: t("plans.invalid_expiry"),
        }));
        hasErrors = true;
      } else {
        setErrors((prevErrors) => ({ ...prevErrors, expiry: "" }));
      }

      // Handling CVC validation
      const cvcElement = elements.getElement(CardCvcElement);
      if (cvcElement._complete === false) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          cvc: t("plans.invalid_cvv"),
        }));
        hasErrors = true;
      } else {
        setErrors((prevErrors) => ({ ...prevErrors, cvc: "" }));
      }

      // If there are errors, stop the process and don't call the API
      if (hasErrors) {
        setIsloading(false);
        return;
      }

      // Clear previous general error messages
      setErrors((prevErrors) => ({ ...prevErrors, general: "" }));

      // Create a payment method
      const paymentMethod = await stripe?.createPaymentMethod({
        type: "card",
        card: elements?.getElement(CardNumberElement),
        billing_details: {
          name: user_plan?.name,
        },
      });

      const methodID = paymentMethod?.error?.message;

      if (methodID) {
        toast.error(methodID, {
          toastId: "copy-success",
        });
        return;
      }

      // Call the backend to create subscription
      const subscriptionData = {
        user: parseInt(userID),
        currency: user_plan?.selecteditem,
        package: user_plan?.id,
        interval: user_plan?.selectedType,
        stripePaymentMethodID: paymentMethod?.paymentMethod?.id,
        stripeProductID: user_plan?.productID,
        stripePriceID:
          user_plan?.selectedType === "year"
            ? user_plan?.yearlyID
            : user_plan?.priceID,
        description: "Subscription ................",
        ...(mycouponcode &&
          discountedPackages?.includes(user_plan?.code) && {
            coupon: mycouponcode,
          }),
      };

      http
        .request({
          method: "post",
          maxBodyLength: Infinity,
          url: "/subscriptions",
          data: JSON.stringify(subscriptionData),
        })
        .then(async (response) => {
          const confirmPayment = await stripe?.confirmCardPayment(
            response.data.clientSecret
          );
          if (confirmPayment?.error) {
            alert(confirmPayment.error.message);
          } else {
            dispatch(setPackageData(response?.data?.code));
            refetch();
            setTimeout(() => {
              setPaymentDone(true);
            }, 1500);
            setTimeout(() => {
              setIsloading(false);
              setPaymentOpen(false);
            }, 2000);
          }
          navigate(location.pathname, { replace: true });
          localStorage.removeItem("DSC_OBJ");
          setUserData(0);
          dispatch(setFreeToFalse(false));
          setImmediateUpgrade(false);
          setMyCouponPrice(null);
          setMyCouponCode("");
          refetchCoupon();
        })
        .catch(function (error) {
          if (error.response) {
            setError(true);
            const errorMessage = error?.response?.data?.message;

            if (Array.isArray(errorMessage)) {
              setTimeout(() => {
                toast.error(errorMessage[0], {
                  toastId: "copy-success",
                });
              }, 500);
            } else if (typeof errorMessage === "string") {
              setTimeout(() => {
                toast.error(errorMessage, {
                  toastId: "copy-success",
                });
              }, 500);
            } else {
              toast.error(t("plans.an_error_occurred"), {
                toastId: "copy-success",
              });
            }
            setIsloading(false);
          } else if (error.request) {
            console.log(error.request);
            setError(true);
            setErrorMessage(error.request);
            setIsloading(false);
          } else {
            setError(true);
            setErrorMessage(error.message);
            setIsloading(false);
          }
        });
    } catch (error) {
      setIsloading(false);
    }
  };
  //#endregion

  //#region create subscription
  const updateSubscription = async (e) => {
    e.preventDefault();
    try {
      setIsloading(true);

      // Error handling flag
      let hasErrors = false;

      // Card validation logic
      const validateCardElement = (element, errorKey, errorMessage) => {
        if (!element?._complete) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            [errorKey]: errorMessage,
          }));
          hasErrors = true;
        } else {
          setErrors((prevErrors) => ({ ...prevErrors, [errorKey]: "" }));
        }
      };

      const cardElement = elements.getElement(CardNumberElement);
      const expiryElement = elements.getElement(CardExpiryElement);
      const cvcElement = elements.getElement(CardCvcElement);

      validateCardElement(
        cardElement,
        "cardNumber",
        t("plans.invalid_card_number")
      );
      validateCardElement(expiryElement, "expiry", t("plans.invalid_expiry"));
      validateCardElement(cvcElement, "cvc", t("plans.invalid_cvv"));

      // Stop execution if there are validation errors
      if (hasErrors) {
        setIsloading(false);
        return;
      }

      // Clear general errors
      setErrors((prevErrors) => ({ ...prevErrors, general: "" }));

      // Create payment method
      const paymentMethod = await stripe?.createPaymentMethod({
        type: "card",
        card: elements?.getElement(CardNumberElement),
        billing_details: {
          name: user_plan?.name,
        },
      });

      const methodID = paymentMethod?.paymentMethod?.id;

      const methodMessage = paymentMethod?.error?.message;
      if (methodMessage) {
        toast.error(methodMessage, {
          toastId: "copy-success",
        });
        return;
      }

      // Attach card using mutateAsync
      const attachCardData = { id: userID, methodID };

      try {
        await mutateAsync(attachCardData);
      } catch (error) {
        setIsloading(false);
        const errorMessage =
          error?.response?.data?.message ||
          t("plans.error_attaching_payment_method");
        toast.error(errorMessage, { toastId: "attach-error" });
        return;
      }

      // Call backend to create subscription
      const subscriptionData = {
        user: parseInt(userID),
        currency: user_plan?.selecteditem,
        package: user_plan?.id,
        interval: user_plan?.selectedType,
        stripePaymentMethodID: methodID,
        stripeProductID: user_plan?.productID,
        subscription: subscriptionID,
        stripePriceID:
          user_plan?.selectedType === "year"
            ? user_plan?.yearlyID
            : user_plan?.priceID,
        description: "Subscription ................",
        ...(mycouponcode &&
          discountedPackages?.includes(user_plan?.code) && {
            coupon: mycouponcode,
          }),
      };

      try {
        const response = await http.request({
          method: "post",
          maxBodyLength: Infinity,
          url: "/subscriptions/upgrade",
          data: JSON.stringify(subscriptionData),
        });

        if (freeCheck === "free") {
          dispatch(setPackageData("free"));
        } else {
          dispatch(setPackageData(response?.data?.code));
        }
        refetch();
        if (user_plan?.name === "Free forever") {
          HandleAddons();
        } else {
          setTimeout(() => {
            setPaymentDone(true);
            setIsloading(false);
            setPaymentOpen(false);
          }, 2000);
        }
        navigate(location.pathname, { replace: true });
        localStorage.removeItem("DSC_OBJ");
        dispatch(setFreeToFalse(false));
        setImmediateUpgrade(false);
        setUserData(0);
        refetchCoupon();
        setMyCouponPrice(null);
        setMyCouponCode("");
      } catch (error) {
        setIsloading(false);
        const errorMessage =
          error?.response?.data?.message ||
          t("plans.subscription_upgrade_failed");
        toast.error(errorMessage, { toastId: "subscription-error" });
      }
    } catch (error) {
      setIsloading(false);
      toast.error(t("plans.unexpected_error"), {
        toastId: "unexpected-error",
      });
    }
  };
  //#endregion

  //#region useMutation with mutateAsync
  const { mutateAsync } = useMutation(attachCard, {
    onSuccess: () => {
      queryClient.invalidateQueries("/subscriptions/attachPaymentMethod");
    },
  });
  //#endregion

  //#region Purchase add-on
  const HandleAddons = () => {
    let data = {
      addOnId: addOnId,
      addOnPriceIds: addOnPriceIds,
      description: "Monthly premium reports",
      currency: currency,
      interval: "monthly",
      coupon: "DISCOUNT2024",
    };
    try {
      addonsMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };

  const { mutate: addonsMutate, isLoading: addonsmutateLoading } = useMutation(
    addonsUpgrade,
    {
      onSuccess: (res) => {
        queryClient.invalidateQueries("/subscriptions/upgrade");
        if (onlyAddonPurchase) {
          toast.success(t("plans.purchase_successful"), {
            toastId: "copy-success",
            style: { width: "450px" },
          });
        } else {
          toast.success(t("plans.package_changed"), {
            toastId: "copy-success",
            style: { width: "450px" },
          });
        }
        toast.success(t("plans.package_changed"), {
          toastId: "copy-success",
          style: { width: "450px" },
        });
        setTimeout(() => {
          setPaymentDone(true);
          setIsloading(false);
          setPaymentOpen(false);
          setOnlyAddonPurchase(false);
        }, 2000);
      },
      onError: (err) => {
        console.log(err?.response?.data?.message[0], "error on add-ons");
        setIsloading(false);

        const errorMessage = err?.response?.data?.message;

        if (Array.isArray(errorMessage)) {
          setTimeout(() => {
            toast.error(errorMessage[0], {
              toastId: "copy-success",
            });
          }, 500);
        } else if (typeof errorMessage === "string") {
          setTimeout(() => {
            toast.error(errorMessage, {
              toastId: "copy-success",
            });
          }, 500);
        } else {
          toast.error(t("plans.an_error_occurred"), {
            toastId: "copy-success",
          });
        }
      },
    }
  );
  //#endregion
  return (
    <>
      <div className="flex md:w-1/3 bg-[#F0F0F0] shadow mt-4 mx-auto p-4 flex-col">
        <div className="flex flex-col">
          <h1
            className="font-bold text-xl"
            style={{ fontFamily: "Archia Bold" }}
          >
            {user_plan?.name === "Free forever" ? (
              t("plans.downgrade_with_data_addon")
            ) : (
              <>
                {user_plan?.name}{" "}
                {user_plan?.selectedType === "month"
                  ? t("plans.monthly_plan")
                  : t("plans.annual_plan")}{" "}
              </>
            )}
          </h1>
          <p style={{ fontFamily: "Silka" }}>
            {user_plan?.selectedType === "month"
              ? t("plans.monthly_payment")
              : t("plans.annual_payment")}
          </p>
        </div>
        <div className="flex justify-between mt-8">
          <div className="flex flex-col ">
            <h1
              style={{ fontFamily: "Archia Bold" }}
              className="font-bold text-2xl flex flex-row gap-1"
            >
              {mycouponprice ? (
                <div className="flex flex-row gap-2">
                  {discountedPackages?.includes(user_plan?.code) && (
                    <div className="relative flex font-normal text-lg my-auto text-red-800">
                      <span className="relative z-10">
                        {user_plan?.selectedSign}
                        {user_plan?.name === "Free forever" ? (
                          "100 "
                        ) : (
                          <>
                            {Intl.NumberFormat("en-US", {
                              style: "decimal",
                              minimumFractionDigits: 0,
                              // minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(
                              mycouponprice
                                ? user_plan?.selectedAmount
                                : user_plan?.selectedAmount
                            )}{" "}
                          </>
                        )}
                      </span>
                      <span className="absolute left-0 top-3 w-full h-[2px] bg-red-800 transform rotate-[-10deg]"></span>
                    </div>
                  )}
                  {user_plan?.selectedSign}
                  {user_plan?.name === "Free forever" ? (
                    <>{addonPrice}</>
                  ) : (
                    <>
                      {Intl.NumberFormat("en-US", {
                        style: "decimal",
                        minimumFractionDigits: 0,
                        minimumFractionDigits: 2,
                        // maximumFractionDigits: 2,
                      }).format(
                        mycouponprice
                          ? user_plan?.selectedAmount *
                              (1 - mycouponprice / 100)
                          : user_plan?.selectedAmount
                      )}{" "}
                    </>
                  )}
                </div>
              ) : (
                <>
                  {user_plan?.selectedSign}
                  {user_plan?.name === "Free forever" ? (
                    <>{addonPrice}</>
                  ) : (
                    <>
                      {Intl.NumberFormat("en-US", {
                        style: "decimal",
                        minimumFractionDigits: 0,
                        // minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(
                        user_plan?.mycouponprice
                          ? user_plan?.selectedAmount *
                              (1 - user_plan?.mycouponprice / 100)
                          : user_plan?.selectedAmount
                      )}{" "}
                    </>
                  )}
                </>
              )}
              {user_plan?.selectedType === "month" ? (
                <span
                  style={{ fontFamily: "Archia Semibold" }}
                  className="text-[14px] mt-1"
                >
                  {t("per_month")}
                </span>
              ) : (
                <span className="text-[14px] mt-1">per year</span>
              )}
            </h1>
          </div>
        </div>
      </div>
      <hr className=" w-1/3 bg-black border-1 mx-auto" />
      <div className="flex md:w-1/3 bg-[#F0F0F0] shadow mt-8 mx-auto flex-col p-8">
        <div className="p-4 border border-[#ddd] rounded-lg">
          <form style={{ fontFamily: "Silka" }}>
            <div>
              <p className="text-[0.93rem] text-[#30313d]">
                {t("plans.card_number")}
              </p>
              <div className="transform mt-1 shadow bg-white focus:shadow transition duration-500 focus:scale-105 rounded w-full py-3 px-3 text-gray-700 leading-tight">
                <div style={{ position: "relative" }}>
                  <CardNumberElement
                    options={{
                      style: {
                        base: inputStyle,
                      },
                    }}
                  />
                  <img
                    src={Cardimg}
                    alt="Card Image"
                    className="w-6 h-6"
                    style={{
                      position: "absolute",
                      top: "50%",
                      right: "10px",
                      transform: "translateY(-50%)",
                    }}
                  />
                </div>
              </div>
              {errors.cardNumber && (
                <div className="ml-1">
                  <p className="text-rose-500 sm:text-xs md:text-sm fade-in-image">
                    {errors.cardNumber}
                  </p>
                </div>
              )}
              <div className="w-full mt-3 flex flex-row gap-3">
                <div className="w-1/2">
                  <p className="text-[0.93rem] text-[#30313d]">
                    {t("plans.expiry")}
                  </p>
                  <div className="transform mt-1 shadow bg-white focus:shadow transition duration-500 focus:scale-105 rounded w-full py-3 px-3 text-gray-700 leading-tight">
                    <div style={{ position: "relative" }}>
                      <CardExpiryElement
                        options={{
                          style: {
                            base: inputStyle,
                          },
                        }}
                      />
                      <img
                        src={calendar}
                        alt="Card Image"
                        className="w-6 h-6"
                        style={{
                          position: "absolute",
                          top: "50%",
                          right: "10px",
                          transform: "translateY(-50%)",
                        }}
                      />
                    </div>
                  </div>
                  {errors.expiry && (
                    <div className="ml-1">
                      <p className="text-rose-500 sm:text-xs md:text-sm fade-in-image">
                        {errors.expiry}
                      </p>
                    </div>
                  )}
                </div>
                <div className="w-1/2">
                  <p className="text-[0.93rem] text-[#30313d]">
                    {t("plans.cvc")}
                  </p>
                  <div className="transform mt-1 shadow bg-white focus:shadow transition duration-500 focus:scale-105 rounded w-full py-3 px-3 text-gray-700 leading-tight">
                    <div style={{ position: "relative" }}>
                      <CardCvcElement
                        options={{
                          style: {
                            base: inputStyle,
                          },
                        }}
                      />
                      <img
                        src={cvc}
                        alt="Card Image"
                        className="w-6 h-6"
                        style={{
                          position: "absolute",
                          top: "50%",
                          right: "10px",
                          transform: "translateY(-50%)",
                        }}
                      />
                    </div>
                  </div>
                  {errors.cvc && (
                    <div className="ml-1">
                      <p className="text-rose-500 sm:text-xs md:text-sm fade-in-image">
                        {errors.cvc}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {errors.general && (
              <div className="ml-1">
                <p className="text-rose-500 sm:text-xs md:text-sm fade-in-image">
                  {errors.general}
                </p>
              </div>
            )}
          </form>
          <p
            className="text-xs mt-5 text-[#6d6e78]"
            style={{ fontFamily: "Silka" }}
          >
            {t("plans.by_providing_card")}
          </p>
        </div>
        <div className="flex flex-row mx-auto gap-1 mb-4 mt-3">
          <CiCreditCard1 className="w-6 h-8" />
          <p className="my-auto" style={{ fontFamily: "Silka" }}>
            {t("plans.secure_checkout")}
          </p>
        </div>
        <button
          className="inline-flex items-center w-1/3 2xl:w-1/4 py-2 mx-auto border border-coalColor justify-center px-4 bg-primaryGreen hover:bg-coalColor hover:text-white text-coalColor text-sm font-medium rounded-md"
          type="submit"
          style={{ fontFamily: "Silka" }}
          onClick={(e) => {
            if (shouldUpdate) {
              if (onlyAddonPurchase) {
                HandleAddons();
              } else {
                updateSubscription(e);
              }
            } else {
              createSubscription(e);
            }
          }}
        >
          {loading || addonsmutateLoading ? (
            <span className="flex items-center justify-center">
              <Loader type="Oval" color="black" height={15} width={15} />
              <span className="ml-2">{t("plans.subscribing")}</span>
            </span>
          ) : (
            t("plans.subscribe")
          )}
        </button>
      </div>
    </>
  );
};

export default CheckoutPayment;
