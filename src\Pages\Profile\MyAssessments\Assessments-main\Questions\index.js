import React, { useEffect, useMemo, useRef, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import "../../../../../Components/Loading/Loading2.css";
import { useMutation } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import queryString from "query-string";
import "../../../../../Components/Loading/Loading7.css";
import "../invite.css";
import { useSelector, useDispatch } from "react-redux";
import { getAssessmentByID } from "../hooks/getAssessmentByID";
import { createCustomQuestions } from "../hooks/createCustomQuestions";
import { useQuery } from "@tanstack/react-query";
import King from "../../../../../Assets/preee.png";
import http from "../../../../../http";
import { toast, ToastContainer, Zoom } from "react-toastify";
import PremiumGeneral from "../../../../../Components/Modals/PremiumGeneral";
import { FaPlus } from "react-icons/fa6";
import { setNextQuestionsToFalse } from "../../../../../redux/reducers/NextQuestions/NextQuestionsSlice";
import QuestionsModal from "./QuestionsModal";
import { TimeData } from "./data";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import { getLibraryQuestions } from "../hooks/getLibraryQuestions";
import { VscLibrary } from "react-icons/vsc";
import info from "../../../../../Dexta_assets/helpIcon.png";
import eye from "../../../../../Dexta_assets/eye.png";
import bin from "../../../../../Dexta_assets/bin.png";
import DeleteIcon from "../../../../../Dexta_assets/deleteIcon.png";
import DeleteModal from "../../../../../Components/ConfirmationModals/DeleteModal";
import { deleteSection } from "../hooks/deleteSection";
import { createCustomSet } from "../hooks/createCustomSet";
import { updateStep } from "../hooks/updateStep";
import { FaEdit } from "react-icons/fa";
import { FaRegEdit } from "react-icons/fa";
import { MdModeEditOutline } from "react-icons/md";
import { BsThreeDotsVertical } from "react-icons/bs";
import { PiDotsThreeCircle } from "react-icons/pi";
import { PiDotsThreeOutlineFill } from "react-icons/pi";
import useWindowSize from "../../../../../Helpers/useWindowSize";
import useOutsideClick from "../../../../../Components/OutsideClick/OutsideClick";
import { deleteLibraryQuestion } from "../hooks/deleteQuestion";
import SearhBar from "../../../../../Dexta_assets/searchBar.png";
import { useTranslation } from "react-i18next";
import CustomModulesOverview from "./CustomModulesOverview";
import { getModuleByID } from "../hooks/getModuleByID";
import { FaQuestionCircle, FaInfoCircle } from "react-icons/fa";

const Questions = (props) => {
  const { t } = useTranslation();
  const [questionSetName, setQuestionSetName] = useState("");
  const [questionSetTime, setQuestionSetTime] = useState("");
  const queryClient = useQueryClient();
  const [errorMessage, setErrorMessage] = useState("");
  const [error, setError] = useState(false);
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const assessment_id = localStorage.getItem("assessment_ID");
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const [premiumGeneralOpen, setPremiumGeneral] = useState(false);
  const [anchorEl2, setAnchorEl2] = React.useState(null);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const next = useSelector((state) => state.nextQuestion.setNextQuestions);
  const [questions, setQuestions] = useState([]);
  const [showQuestion, setShowQuestion] = useState(false);
  const [questionIndex, setQuestionIndex] = useState(null);
  const [questionModal, setQuestionModal] = useState(false);
  const [timeDropdown, setTimeDropdown] = useState(false);
  const [timeTitle, setTimeTitle] = useState("");
  const TimeRef = useRef(null);
  const dispatch = useDispatch();
  const [createQuestionLoading, setCreateQuestionLoading] = useState(false);
  const [loadingIndex, setLoadingIndex] = useState(null);
  const [questionData, setQuestionData] = useState(null);
  const [deleteModal, setDeleteModal] = useState(false);
  const [questionID, setQuestionID] = useState(0);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [lastQuestionDelete, setLastQuestionDelete] = useState(false);
  const [adminLibrary, setAdminLibrary] = useState(false);
  const [fakeLoading, setFakeLoading] = useState(false);
  const [downloadDropdowns, setDownloadDropdowns] = useState({});
  const downloadRef = useRef(null);
  const [libraryQuestion, setLibraryQuestion] = useState(false);
  const [searchedValue, setSearchedValue] = useState("");
  const [isOverviewMode, setIsOverviewMode] = useState(true);
  const [customModuleCount, setCustomModuleCount] = useState(0);
  const [customModulesData, setCustomModulesData] = useState([]);
  const size = useWindowSize();
  const isMobile = size.width <= 640;
  const language = localStorage.getItem("i18nextLng");
  const prevQuestionsRef = useRef();


  //#region Submit test
  const handleSubmit = () => {
    let data = {
      content: {
        status: "active",
        notes: "complete",
      },
      categoryID:
        parsed && parsed.assessment_id !== undefined
          ? parsed.assessment_id
          : assessment_id,
    };
    try {
      submitMutate(data);
    } catch (err) {
      //
    }
  };
  const { mutate: submitMutate, isLoading: submitLoading } = useMutation(
    updateStep,
    {
      onSuccess: () => {
        queryClient.invalidateQueries("assessment");
      },
      onError: (error) => { },
    }
  );
  //#endregion

  const handleModulesIDS = (id) => {
    if (props.data.modulesID.includes(id)) {
      return;
    }
    const newProps = { ...props.data };
    let newModuleID = newProps.customQuestion;
    newModuleID = id;
    newProps["customQuestion"] = newModuleID;
    props.setData(newProps);
  };

  const { mutate, isLoading: mutateLoad } = useMutation(createCustomQuestions, {
    onSuccess: (response) => {
      console.log("custom mod created");
      const { data } = response;
      handleModulesIDS(data?.id);
      queryClient.invalidateQueries("assessment");
      dispatch(setNextQuestionsToFalse(false));
      props.setQuestionLoading(false);
      setQuestionIndex(null);
      setQuestionModal(true);
      // Increment the custom module count immediately
      setCustomModuleCount(prevCount => prevCount + 1);
    },
    onError: (error) => {
      setError(true);
      setErrorMessage(error.response.data.message[0]);
      if (Array.isArray(error.response.data.message)) {
        toast.error(error.response.data.message[0], {
          toastId: "copy-success",
        });
      } else if (typeof error.response.data.message === "string") {
        toast.error(error.response.data.message, {
          toastId: "copy-success",
        });
      } else {
        toast.error("An error occurred.", {
          toastId: "copy-success",
        });
      }
    },
  });

  const {
    data: data_assessments,
    error: assessmentError,
    isLoading: assessmentLoading,
    refetch,
  } = useQuery(["assessment_new"], () =>
    getAssessmentByID(
      parsed.assessment_id != null
        ? parsed.assessment_id
        : parseInt(assessment_id)
    )
  );

  const {
    data: sectionData,
    isLoading: sectionDataLoading,
    error: sectionError,
    refetch: refetchSection,
  } = useQuery(
    ["section", props.data?.customQuestion, language],
    () => {
      const sectionId = props.data?.customQuestion;
      if (typeof sectionId !== "number" || isNaN(sectionId)) {
        return undefined;
      }
      return getModuleByID(sectionId, language);
    },
  );

  // Populate fields from sectionData when editing an existing module
  useEffect(() => {
    if (!isOverviewMode && sectionData && props.data?.customQuestion) {
      refetch();
      const sectionId = props.data?.customQuestion;
      const foundSection = data_assessments?.customQuestionsList?.find(
        (section) => section.id === sectionId || String(section.id) === String(sectionId)
      );
      if (foundSection) {
        setQuestionSetName(foundSection.name || "");
        setQuestionSetTime(foundSection.time || "");

        // Update questions and clear loading when questions are actually updated
        const newQuestions = foundSection?.questions || [];
        const prevQuestions = prevQuestionsRef.current || [];
        const questionsChanged = JSON.stringify(newQuestions) !== JSON.stringify(prevQuestions);

        setQuestions(newQuestions);
        prevQuestionsRef.current = newQuestions;

        // Clear loading state when questions are actually updated
        if (questionsChanged && (loadingIndex !== null || fakeLoading)) {
          setLoadingIndex(null);
          setFakeLoading(false);
        }
      }
    }
  }, [sectionData, data_assessments, assessmentLoading, questionModal, isOverviewMode, props.data?.customQuestion, loadingIndex, fakeLoading]);

  // Set overview mode when editing an existing module
  useEffect(() => {
    if (props.data?.customQuestion && props.data?.customQuestionDetail) {
      setIsOverviewMode(false);
    }
  }, [props.data?.customQuestion, props.data?.customQuestionDetail]);

  const removeInlineStyles = (html) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    // Remove style attributes
    const elementsWithStyle = doc.querySelectorAll("[style]");
    elementsWithStyle.forEach((element) => {
      element.removeAttribute("style");
    });

    // Remove strong tags
    const strongTags = doc.querySelectorAll("strong");
    strongTags.forEach((tag) => {
      const parent = tag.parentNode;
      while (tag.firstChild) {
        parent.insertBefore(tag.firstChild, tag);
      }
      parent.removeChild(tag);
    });

    // Remove img tags
    const imgTags = doc.querySelectorAll("img");
    imgTags.forEach((img) => {
      img.parentNode.removeChild(img);
    });

    return doc.documentElement.innerHTML;
  };

  const handleAddCustom = () => {
    // Always check for empty fields first
    if (
      questionSetName == "" ||
      questionSetName == null ||
      questionSetName == undefined
    ) {
      toast.error("Questions set name can not be empty.", {
        toastId: "copy-success",
      });
    } else if (
      questionSetTime == "" ||
      questionSetTime == null ||
      questionSetTime == undefined
    ) {
      toast.error("Questions set time can not be empty.", {
        toastId: "copy-success",
      });
    } else {
      // If there's no existing module, create a new one
      if (props?.data?.customQuestion === null) {
        // Check if a module with the same name already exists
        const existingModules = customModulesData || [];
        const isDuplicateName = existingModules.some(
          module => module.name && module.name.toLowerCase().trim() === questionSetName.toLowerCase().trim()
        );

        if (isDuplicateName) {
          toast.error(t("create_test.questions.errors.duplicate_module_name"), {
            toastId: "duplicate-module-name",
          });
          return;
        }

        let data = {
          name: questionSetName,
          time: questionSetTime,
          assessmentId:
            parsed.assessment_id != null
              ? parseInt(parsed.assessment_id)
              : parseInt(assessment_id),
        };
        try {
          mutate(data);
        } catch (err) {
          //
        }
      } else {
        // If there's an existing module, open the question modal to add questions
        setQuestionModal(true);
      }
    }
  };

  // useEffect(() => {
  //   if (!assessmentLoading && !data_assessments?.customQuestionsList?.name) {
  //     setQuestionSetName("");
  //   }
  // }, [data_assessments]);

  const handleNext = () => {
    // If user is not in overview mode, first take them to overview mode
    if (!isOverviewMode) {
      setIsOverviewMode(true);
      // Clean up any editing state
      setQuestionSetName("");
      setQuestionSetTime("");
      setQuestions([]);
      setQuestionModal(false);
      setQuestionIndex(null);
      // Clear any loading states
      setLoadingIndex(null);
      setFakeLoading(false);
      props.setQuestionLoading(false);
      // Reset the custom module data to ensure clean overview
      props.setData({
        ...props.data,
        customQuestion: null,
        customQuestionDetail: null,
      });
      dispatch(setNextQuestionsToFalse(false));
      return;
    }

    // If in overview mode, proceed with normal flow
    props.setQuestionLoading(true);
    if (questions.length > 0) {
      if (
        questionSetName == "" ||
        questionSetName == null ||
        questionSetName == undefined
      ) {
        toast.error("Questions set name can not be empty.", {
          toastId: "copy-success",
        });
      } else if (
        questionSetTime == "" ||
        questionSetTime == null ||
        questionSetTime == undefined
      ) {
        toast.error("Questions set time can not be empty.", {
          toastId: "copy-success",
        });
      } else {
        let data = {
          questions: questions?.map((q) => q?.id),
          name: questionSetName,
          time: questionSetTime,
          assessmentId:
            parsed.assessment_id != null
              ? parseInt(parsed.assessment_id)
              : parseInt(assessment_id),
        };
        try {
          mutate(data);
          props.setselecteditem("candidates");
          localStorage.setItem("current_module", "candidates");
          setQuestionSetName("");
          handleSubmit();
        } catch (err) {
          //
        }
      }
    } else {
      sectionDelete(props?.data?.customQuestion);
      setTimeout(() => {
        props.setselecteditem("candidates");
        localStorage.setItem("current_module", "candidates");
        props.setQuestionLoading(false);
        dispatch(setNextQuestionsToFalse(false));
        handleSubmit();
      }, 500);
    }
  };

  useEffect(() => {
    if (next) {
      handleNext();
    }
  }, [next]);

  const deleteQuestion = (id) => {
    setDeleteLoading(true);
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `/questions/${id}/sections/${props?.data?.customQuestion}`,
      headers: {
        accept: "*/*",
      },
    };
    http
      .request(config)
      .then((response) => {
        const newElements = [...questions];
        const extractIDs = (arr) => arr.map((obj) => obj.id);
        const ids = extractIDs(newElements);
        const index = ids.indexOf(id);
        if (index !== -1) {
          newElements.splice(index, 1);
          setQuestions(newElements);
          setDeleteLoading(false);
          setDeleteModal(false);
        }
        if (lastQuestionDelete) {
          sectionDelete(props?.data?.customQuestion);
        }
        setQuestionIndex(null);
        setDeleteLoading(false);
        setDeleteModal(false);
      })
      .catch((error) => {
        console.log(error);
        setDeleteLoading(false);
        setDeleteModal(false);
      });
  };

  //#region Handling clicks outside ref
  useEffect(() => {
    function HandleOutside(event) {
      if (
        TimeRef.current &&
        !TimeRef.current.contains(event.target) &&
        event.target.id !== "time" &&
        !event.target.closest(".TimeClass")
      ) {
        setTimeDropdown(false);
      }
    }
    document.addEventListener("mousedown", HandleOutside);
    return () => {
      document.removeEventListener("mousedown", HandleOutside);
    };
  }, [TimeRef]);

  //#endregion

  //#region Fetching questions from library
  const {
    data: libraryData,
    error: libraryError,
    isLoading: libraryLoading,
    refetch: libraryRefetch,
  } = useQuery(
    [
      "/questions/recruiter/custom-questions",
      adminLibrary,
      searchedValue,
      language,
    ],
    () => getLibraryQuestions(adminLibrary, searchedValue, language)
  );
  //#endregion

  //#region Deleting question
  const handleDeleteQuestion = (id) => {
    let data = {
      questionID: id,
    };
    try {
      deleteMutate(data);
    } catch (err) { }
  };

  const { mutate: deleteMutate, isLoading: deleteQuestionLoading } =
    useMutation(deleteLibraryQuestion, {
      onSuccess: () => {
        queryClient.invalidateQueries("questions");
        setLibraryQuestion(false);
        setDeleteModal(false);
      },
    });
  //#endregion

  //#region Add new module from custom library
  const createOption = async (option) => {
    try {
      let cleaObj = option;
      cleaObj = removeEmpty(cleaObj);
      return await http.post(`/options`, cleaObj).then((response) => {
        return response.data;
      });
    } catch (error) {
      console.log("🚀 ~ createOption ~ error:", error);
    }
  };

  function removeEmpty(obj) {
    return Object.entries(obj)
      .filter(([_, v]) => v != null && v !== "")
      .reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {});
  }

  const handleModulesIDSNew = (obj) => {
    const newProps = { ...props?.data };
    let newModuleID = newProps.customQuestionDetail;
    newModuleID = obj;
    newProps["customQuestionDetail"] = newModuleID;
    props?.setData(newProps);
  };

  useEffect(() => {
    if (questionData !== null) {
      AddNewModule(questionData);
    }
  }, [questionData]);

  const AddNewModule = (i) => {
    setCreateQuestionLoading(true);
    setFakeLoading(true);
    setLoadingIndex(libraryData.data.indexOf(i));
    const questionData = {
      section: props?.data?.customQuestion,
      type: i?.type,
      image: i?.image,
      status: "active",
      description: i?.description,
      is_sample: false,
    };
    http
      .post(`/questions`, questionData)
      .then(async (response) => {
        const { data } = response;
        const options = i?.options?.map((ele, index) => {
          return {
            question: data?.id,
            title: ele?.title,
            isCorrectOption: ele?.isCorrectOption,
            optionPosition: ele?.optionPosition,
            status: "active",
          };
        });
        const optionSavedList = await options?.map((opt, index) => {
          return createOption(opt);
        });

        const questionSaveItem = {
          ...data,
          options: await Promise.all(optionSavedList),
        };
        setQuestions([{ ...questionSaveItem }, ...questions]);
        handleModulesIDSNew([{ ...questionSaveItem }, ...questions]);
        setCreateQuestionLoading(false);
        setQuestionData(null);
      })
      .catch((error) => {
        // logAxiosError(error)
        setCreateQuestionLoading(false);
        setQuestionData(null);
        alert("Error: While creating question. Please try again!");
        // setSaveBtnLoading(false)
      });
  };

  useEffect(() => {
    props.setData({ ...props.data, fileBulk: null });
  }, []);

  console.log(props?.data?.customQuestion, "section id")
  const handleAddCustomFromLibrary = (i, index) => {
    setLoadingIndex(index);
    setFakeLoading(true);
    if (props?.data?.customQuestion === null) {
      if (
        questionSetName == "" ||
        questionSetName == null ||
        questionSetName == undefined
      ) {
        toast.error("Questions set name can not be empty.", {
          toastId: "copy-success",
        });
        setLoadingIndex(null);
        setFakeLoading(false);
        return;
      } else if (
        questionSetTime == "" ||
        questionSetTime == null ||
        questionSetTime == undefined
      ) {
        toast.error("Questions set time can not be empty.", {
          toastId: "copy-success",
        });
        setLoadingIndex(null);
        setFakeLoading(false);
        return;
      } else {
        // Check if a module with the same name already exists
        const existingModules = customModulesData || [];
        const isDuplicateName = existingModules.some(
          module => module.name && module.name.toLowerCase().trim() === questionSetName.toLowerCase().trim()
        );

        if (isDuplicateName) {
          toast.error(t("create_test.questions.errors.duplicate_module_name"), {
            toastId: "duplicate-module-name",
          });
          setLoadingIndex(null);
          setFakeLoading(false);
          return;
        }

        let data = {
          name: questionSetName,
          time: questionSetTime,
          assessmentId:
            parsed.assessment_id != null
              ? parseInt(parsed.assessment_id)
              : parseInt(assessment_id),
        };
        try {
          libraryMutate(data, {
            onSuccess: (response) => {
              let Maindata = {
                data: {
                  sectionId: response?.data?.id,
                  questionIds: Array.isArray(i?.id) ? i?.id : [i?.id],
                  name: questionSetName,
                  time: questionSetTime,
                  assessmentId:
                    parsed.assessment_id != null
                      ? parseInt(parsed.assessment_id)
                      : parseInt(assessment_id),
                },
                section: {
                  id: props?.data?.customQuestion,
                },
              };
              mutateLibrary(Maindata);
            },
          });
        } catch (err) {
          //
        }
      }
    } else {
      let Maindata = {
        data: {
          sectionId: props?.data?.customQuestion,
          questionIds: Array.isArray(i?.id) ? i?.id : [i?.id],
          name: questionSetName,
          time: questionSetTime,
          assessmentId:
            parsed.assessment_id != null
              ? parseInt(parsed.assessment_id)
              : parseInt(assessment_id),
        },
        section: {
          id: props?.data?.customQuestion,
        },
      };
      mutateLibrary(Maindata);
      // setQuestionData(i);
    }
  };

  const { mutate: libraryMutate, isLoading: libraryLoad } = useMutation(
    createCustomQuestions,
    {
      onSuccess: (response) => {
        const { data } = response;
        handleModulesIDS(data?.id);
        queryClient.invalidateQueries("assessment");
        // Increment the custom module count immediately
        setCustomModuleCount(prevCount => prevCount + 1);
        // Loading state will be cleared when questions are actually updated in useEffect
      },
      onError: (error) => {
        setError(true);
        setErrorMessage(error.response.data.message[0]);
        // Clear loading state on error
        setLoadingIndex(null);
        setFakeLoading(false);
      },
    }
  );

  const { mutate: mutateLibrary, isLoading: mutateLoading } = useMutation(
    createCustomSet,
    {
      onSuccess: (response) => {
        console.log("custom mod created");
        const { data } = response;
        handleModulesIDS(data?.id);

        // Automatically add the newly created custom module to selectedModules
        if (data?.id && data?.name) {
          const newModule = {
            id: data.id,
            name: data.name,
            time: parseInt(data.time) || 0,
            isCustom: true
          };

          // Check if module is not already in selectedModules
          if (!props.data.selectedModules.some(m => m.id === data.id)) {
            const newSelectedModules = [...props.data.selectedModules, newModule];
            props.setData({
              ...props.data,
              selectedModules: newSelectedModules,
              customQuestion: data.id
            });
          }
        }

        queryClient.invalidateQueries("assessment");
        // Loading state will be cleared when questions are actually updated in useEffect
      },
      onError: (error) => {
        setError(true);
        setErrorMessage(error.response.data.message[0]);
        // Clear loading state on error
        setLoadingIndex(null);
        setFakeLoading(false);
      },
    }
  );

  const { mutate: sectionDelete } = useMutation(
    deleteSection,
    {
      onSuccess: () => {
        queryClient.invalidateQueries("section");
        setLastQuestionDelete(false);
        props.setData({ ...props.data, customQuestion: null });
        setQuestionSetName("");
      },
      onError: (error) => {
        setError(true);
        setErrorMessage(error.response.data.message[0]);
      },
    }
  );
  //#endregion

  const previewModule = (id) => {
    const url = `/preview/question/${id}`;
    window.open(url, "_blank");
  };

  const handleCreateNewModule = () => {
    setIsOverviewMode(false);
    setQuestionSetName("");
    setQuestionSetTime("");
    setQuestions([]);
    // Completely reset the custom module data to ensure no conflicts
    props.setData({
      ...props.data,
      customQuestion: null,
      customQuestionDetail: null,
    });
    // Also reset any related state that might cause conflicts
    setQuestionIndex(null);
    setQuestionModal(false);
  };

  const handleBackToOverview = () => {
    setIsOverviewMode(true);
  };

  useOutsideClick(
    [
      {
        ref: downloadRef,
        excludeClasses: [".downloadClass"],
        excludeIds: ["downloadID", "labelID"],
      },
    ],
    (ref) => {
      if (ref === downloadRef) setDownloadDropdowns({});
    }
  );

  //#region take user to top of screen
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  //#endregion

  return (
    <div>
      <div className="sm:px-0 md:px-2">
        <PremiumGeneral
          premiumGeneralOpen={premiumGeneralOpen}
          setPremiumGeneral={setPremiumGeneral}
        />
        <QuestionsModal
          questionModal={questionModal}
          setQuestionModal={setQuestionModal}
          showQuestion={showQuestion}
          setQuestionShow={setShowQuestion}
          questionIndex={questionIndex}
          questions={questions}
          setQuestions={setQuestions}
          setData={props?.setData}
          data={props?.data}
          setQuestionIndex={setQuestionIndex}
          libraryRefetch={libraryRefetch}
        />
        {deleteModal && (
          <DeleteModal
            setDeleteModal={setDeleteModal}
            onClick={() =>
              libraryQuestion
                ? handleDeleteQuestion(questionID)
                : deleteQuestion(questionID, questionIndex)
            }
            header={
              libraryQuestion
                ? t(
                  "create_test.questions.delete_modal_table.delete_question_header"
                )
                : t(
                  "create_test.questions.delete_modal_table.remove_question_header"
                )
            }
            icon={DeleteIcon}
            loading={deleteLoading}
            description={t(
              "create_test.questions.delete_modal_table.delete_question_description"
            )}
            buttonText={
              libraryQuestion
                ? t(
                  "create_test.questions.delete_modal_table.delete_question_button"
                )
                : t(
                  "create_test.questions.delete_modal_table.remove_question_button"
                )
            }
          />
        )}

        {isOverviewMode ? (
          <CustomModulesOverview
            setselecteditem={props.setselecteditem}
            setData={props.setData}
            data={props.data}
            setQuestionLoading={props.setQuestionLoading}
            onCreateNewModule={handleCreateNewModule}
            onModuleCountChange={setCustomModuleCount}
            onCustomModulesDataChange={setCustomModulesData}
          />
        ) : (
          <div className="bg-white flex gap-8 p-6 flex-col">
            <div className="gap-8">
              {(assessmentLoading || sectionDataLoading) ? (
                <div class="loader-container-1 w-full">
                  <div class="loader-1"></div>
                </div>
              ) : (
                <div className="flex-1 space-y-2">
                  <div className="flex flex-row gap-5 items-center md:justify-between">
                    <h2
                      className="text-coalColor"
                      style={{ fontFamily: "Archia Semibold" }}
                    >
                      {props.data?.customQuestionDetail?.name
                        ? t("create_test.questions.edit_custom_module")
                        : t("create_test.questions.add_custom_module")
                      }
                    </h2>
                    <button
                      onClick={handleBackToOverview}
                      className="text-sm text-gray-500 hover:text-black hover transition-colors" style={{ fontFamily: "Silka" }}
                    >
                      ← Go back to tests
                    </button>
                  </div>
                  <p
                    className="mt-5 text-sm w-5/6"
                    style={{ fontFamily: "Silka Light" }}
                  >
                    {t("create_test.questions.description")}
                  </p>
                  <div className="flex flex-row">
                    <h1
                      className="font-medium mr-4 mt-4"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("create_test.questions.custom_module_name")}
                    </h1>
                    <div className="group relative flex justify-center mt-4">
                      <img
                        src={info}
                        data-tooltip-target="tooltip-default"
                        className="w-5 h-5 cursor-pointer"
                      />
                      <span className="absolute scale-0 right-0 bottom-10 rounded bg-coalColor left-50 p-2 text-xs w-1008 text-white group-hover:scale-100">
                        {t("create_test.questions.name_tooltip")}
                      </span>
                    </div>
                  </div>
                  <div className="relative w-full border border-1 rounded-lg border-[#D3D5D8] focus:border-coalColor focus-within:border-coalColor">
                    <input
                      type="text"
                      id="customQuestion"
                      autoComplete="false"
                      maxLength="48"
                      className="w-full p-3 sm:text-sm xl:text-base 2x:text-lg rounded-lg outline-none bg-[#F6F7F7]"
                      placeholder={t(
                        "create_test.questions.add_name_placeholder"
                      )}
                      value={questionSetName}
                      onChange={(e) =>
                        user_package_check !== "Enterprise"
                          ? setPremiumGeneral(true)
                          : setQuestionSetName(e.target.value)
                      }
                    />
                  </div>
                  <div className="flex flex-row">
                    <h1
                      className="font-medium mr-4 mt-2"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("create_test.questions.custom_module_time")}
                    </h1>
                    <div className="group relative flex justify-center">
                      <img
                        src={info}
                        data-tooltip-target="tooltip-default"
                        className="w-5 h-5 cursor-pointer mt-2"
                      />
                      <span className="absolute scale-0 right-0 bottom-10 rounded bg-coalColor left-50 p-2 text-xs w-1008 text-white group-hover:scale-100">
                        {t("create_test.questions.time_tooltip")}
                      </span>
                    </div>
                  </div>
                  <div className="">
                    <div>
                      <div className="flex flex-col gap-3 relative">
                        <button
                          id="time"
                          className="TimeClass bg-[#F6F7F7] border border-1 border-[#D3D5D8] focus:border-coalColor focus-within:border-coalColor
                                     sm:py-3 md:py-0 h-1011 focus:outline-[#4A9CB9] rounded-lg text-left inline-flex items-center px-3"
                          type="button"
                          onClick={() =>
                            user_package_check !== "Enterprise"
                              ? setPremiumGeneral(true)
                              : setTimeDropdown(!timeDropdown)
                          }
                        >
                          {questionSetTime && !isNaN(questionSetTime) ? (
                            <span className="sm:text-sm xl:text-base 2x:text-lg ">
                              {questionSetTime} {t("create_test.questions.mins")}
                            </span>
                          ) : (
                            <span className="sm:text-sm xl:text-base 2x:text-lg  text-[#999] font-normal">
                              {t("create_test.questions.set_max_time")}
                            </span>
                          )}
                        </button>
                        {timeDropdown && (
                          <div
                            id="education-drop"
                            className=" absolute TimeClass z-20 border border-coalColor assessmentClass right-0 top-full h-[12rem] overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full mt-2"
                          >
                            {TimeData.map((i) => (
                              <ul
                                key={i.value}
                                className="text-sm text-coalColor rounded hover:bg-coalColor hover:text-white cursor-pointer"
                                style={{ fontFamily: "Silka" }}
                                ref={TimeRef}
                                onClick={() => {
                                  setQuestionSetTime(i?.value);
                                  setTimeDropdown(false);
                                  setTimeTitle(i?.title);
                                }}
                              >
                                <li>
                                  <p className="block px-5 py-2 text-sm font-medium">
                                    {i.title}
                                  </p>
                                </li>
                              </ul>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                    </div>
                  </div>
                  <div className="w-full mt-8">
                    <h3 className="text-coalColor mt-4 text-lg font-bold mb-4" style={{ fontFamily: "Archia Semibold" }}>
                      Selected Questions
                    </h3>
                    <div
                      className="relative px-4 overflow-x-auto bg-white sm:rounded-lg"
                    >
                      <table className="w-full sm:text-xs md:text-sm sm:text-left md:text-left text-white ">
                        <thead
                          className="sm:text-xs md:text-lg text-coalColor bg-white"
                          style={{ fontFamily: "Archia Semibold" }}
                        >
                          <tr>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[10%] sm:py-2"
                            >
                              {t("create_test.questions.table.headers.id")}
                            </th>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[40%] sm:py-2"
                            >
                              {t("create_test.questions.table.headers.question")}
                            </th>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[20%] sm:py-2 "
                            >
                              {t(
                                "create_test.questions.table.headers.correct_options"
                              )}
                            </th>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[10%] sm:py-2"
                            >
                              {t("create_test.questions.table.headers.type")}
                            </th>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[10%] sm:py-2"
                            >
                              {t("create_test.questions.table.headers.status")}
                            </th>
                            <th
                              scope="col"
                              className="sm:px-3 md:px-6 w-[10%] sm:py-2"
                            >
                              {t("create_test.questions.table.headers.image")}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="rounded-lg">
                          {assessmentLoading ? (
                            <tr>
                              <td colSpan="7" className="text-center mt-10">
                                <div className="bg-white">
                                  <div class="loader-container-3">
                                    <div class="loader-3"></div>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          ) : (
                            <>
                              {questions &&
                                questions?.map((i, index) => {
                                  const correctOptions = [];
                                  i?.options?.map((option) => {
                                    if (option?.isCorrectOption) {
                                      return correctOptions.push(option);
                                    }
                                  });
                                  return (
                                    <tr
                                      key={index}
                                      className={`bg-white odd:bg-[#F6F7F7] text-black cursor-pointer`}
                                      style={{ fontFamily: "Silka" }}
                                    >
                                      <td className="md:px-6 sm:px-2 sm:py-3">
                                        {i?.id}
                                      </td>
                                      <td className="sm:px-2 sm:py-3 md:px-6 align-center ">
                                        <div
                                          className="w-full "
                                          style={{
                                            display: "-webkit-box",
                                            WebkitBoxOrient: "vertical",
                                            WebkitLineClamp: 1,
                                            overflow: "hidden",
                                          }}
                                          dangerouslySetInnerHTML={{
                                            __html: removeInlineStyles(i?.description || ""),
                                          }}
                                        />
                                      </td>
                                      <td
                                        className={`md:px-6 sm:px-2 ${i?.type === "Multiple"
                                          ? "text-left"
                                          : "text-justify"
                                          } sm:py-3 flex flex-col my-auto gap-3`}
                                      >
                                        {i?.type === "Multiple" ? (
                                          correctOptions?.map((option, index) =>
                                            option?.title ? (
                                              <p
                                                key={index}
                                                className="my-auto flex items-center"
                                              >
                                                {option.title}
                                              </p>
                                            ) : (
                                              <div
                                                key={index}
                                                className="flex items-center"
                                              >
                                                <img
                                                  src={option?.imageUrl}
                                                  className="h-10 w-10"
                                                  alt={`Option ${index}`}
                                                />
                                              </div>
                                            )
                                          )
                                        ) : correctOptions[0]?.title ? (
                                          <p className="my-auto flex items-center">
                                            {correctOptions[0].title}
                                          </p>
                                        ) : (
                                          <img
                                            src={correctOptions[0]?.imageUrl}
                                            className="h-10 w-10"
                                            alt="Correct Option Image"
                                          />
                                        )}
                                      </td>

                                      <td className="md:px-6 sm:px-2 sm:py-3">
                                        {i?.type === "Single"
                                          ? t("create_test.questions.types.single")
                                          : i?.type === "Multiple"
                                            ? t(
                                              "create_test.questions.types.multiple"
                                            )
                                            : i?.type}
                                      </td>
                                      <td className="md:px-6 sm:px-2 lg:text-left sm:text-center sm:py-3">
                                        {i?.status === "active"
                                          ? t("create_test.questions.status.active")
                                          : i?.status}
                                      </td>
                                      <td className="md:px-6 sm:px-2 sm:py-3 lg:text-left sm:text-center">
                                        {i?.image
                                          ? t(
                                            "create_test.questions.table.content.yes"
                                          )
                                          : t(
                                            "create_test.questions.table.content.no"
                                          )}
                                      </td>
                                      <td className="md:px-6 sm:px-2 sm:py-3 clickable">
                                        <div className="my-auto flex flex-row items-center gap-3 w-[8rem]">
                                          {i?.is_custom_question ? (
                                            <div className="relative group">
                                              <MdModeEditOutline
                                                className="w-4 h-4 my-auto"
                                                onClick={() => {
                                                  setQuestionModal(true);
                                                  setShowQuestion(true);
                                                  setQuestionIndex(index);
                                                }}
                                              />
                                              <div className="tooltipxD right-0 group-hover:block hidden sm:w-[200px] md:w-[200px] text-center absolute top-full opacity-0 pointer-events-none text-xs">
                                                {t(
                                                  "create_test.questions.table.content.not_accessible_tooltip"
                                                )}
                                              </div>
                                            </div>
                                          ) : (
                                            <MdModeEditOutline
                                              className="w-4 h-4 my-auto"
                                              onClick={() => {
                                                setQuestionModal(true);
                                                setShowQuestion(true);
                                                setQuestionIndex(index);
                                              }}
                                            />
                                          )}
                                          <img
                                            src={bin}
                                            className="w-3 h-4 my-auto flex"
                                            onClick={() => {
                                              setQuestionIndex(index);
                                              setLibraryQuestion(false);
                                              setQuestionID(i?.id);
                                              setLastQuestionDelete(
                                                questions?.length === 1
                                                  ? true
                                                  : false
                                              );
                                              setDeleteModal(true);
                                            }}
                                          />
                                          <div className="relative group">
                                            <img
                                              src={eye}
                                              className="w-5 object-contain h-3 my-auto flex"
                                              onClick={() => previewModule(i?.id)}
                                            />
                                            <div className="tooltipxD right-0 group-hover:block hidden w-20 text-center absolute top-full opacity-0 pointer-events-none text-xs">
                                              {t("create_test.questions.preview")}
                                            </div>
                                          </div>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                })}
                            </>
                          )}
                        </tbody>
                      </table>
                      {!assessmentLoading && questions && questions?.length < 1 && (
                        <div className="mt-3 mb-5">
                          <div className="border border-[#FF5812] py-4 rounded">
                            <p
                              className="text-alertRed text-center"
                              style={{ fontFamily: "Silka" }}
                            >
                              {t("create_test.questions.no_custom_questions")}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Bottom Add button (matching CreateCustomTestAI placement) */}
                  <div className="flex mt-4">
                      <div className="ml-auto" style={{ fontFamily: "Silka" }}>
                        <div className="relative ">
                          {user_package_check === "Enterprise" ? (
                            <>
                              <div className="flex relative mt-[4px] gap-4 ">
                                <div className="flex-shrink-0 w-full relative group">
                                  <CustomButton
                                    label={t(
                                      "create_test.questions.add_custom_question"
                                    )}
                                    bgColor="#252E3A"
                                    borderCustom="border border-black text-white"
                                    hoverBgColor="#C0FF06"
                                    hoverTextColor="#252E3A"
                                    disableField={
                                      questions?.length === 50 ||
                                      questionSetName == "" ||
                                      questionSetName == null ||
                                      questionSetName == undefined ||
                                      questionSetTime == "" ||
                                      questionSetTime == null ||
                                      questionSetTime == undefined
                                    }
                                    disabledCheck={
                                      questions?.length === 50 ||
                                      questionSetName == "" ||
                                      questionSetName == null ||
                                      questionSetName == undefined ||
                                      questionSetTime == "" ||
                                      questionSetTime == null ||
                                      questionSetTime == undefined
                                    }
                                    disabledColor="#D3D5D8"
                                    disabledTextColor="#7C8289"
                                    paddingY="0.7rem"
                                    onClickButton={handleAddCustom}
                                  />
                                  {(questionSetName == "" ||
                                    questionSetName == null ||
                                    questionSetName == undefined ||
                                    questionSetTime == "" ||
                                    questionSetTime == null ||
                                    questionSetTime == undefined) && (
                                      <div className="absolute scale-0 group-hover:scale-100 transition-transform duration-200 bg-gray-800 text-white text-xs rounded p-2 w-[20rem] text-center z-10 -top-12 left-1/2 transform -translate-x-1/2" style={{ fontFamily: "Silka" }}>
                                        {t(
                                          "create_test.questions.tooltips.empty_fields"
                                        )}
                                      </div>
                                    )}
                                </div>
                                {questions?.length === 50 && (
                                  <div className="tooltip2 w-[20rem] text-center">
                                    {t(
                                      "create_test.questions.tooltips.max_questions"
                                    )}
                                  </div>
                                )}
                              </div>
                            </>
                          ) : (
                            <div className="flex relative mt-[4px]">
                              <button
                                className={`inline-flex items-center py-[0.8rem] px-4 w-full hover:text-white text-white text-base font-medium rounded-md bg-coalColor hover:bg-coalColor/90`}
                                onClick={() => {
                                  setQuestions([]);
                                  setPremiumGeneral(true);
                                }}
                                style={{ fontFamily: "Silka" }}
                              >
                                <FaPlus className="mr-2" />
                                {t("create_test.questions.add_new_question")}
                                <span className="absolute top-0 right-0 -mt-2 -mr-2">
                                  <img
                                    src={King}
                                    alt="Premium Icon"
                                    className="w-6 h-6"
                                  />
                                </span>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                </div>
              )}
            </div>
            {!isOverviewMode && (
              <>
                {(assessmentLoading || sectionDataLoading) ? (
                  <div class="loader-container-1 w-full">
                    <div class="loader-1"></div>
                  </div>
                ) : (

                  <div className="w-full mt-4">
                    <hr className="w-full bg-bodyColor border-1" />
                    <div className="flex justify-between sm:flex-col md:flex-row">
                      <div className="flex sm:flex-col md:flex-row gap-5 mt-7">
                        <h1
                          className="text-lg my-auto"
                          style={{ fontFamily: "Archia Semibold" }}
                        >
                          {t("create_test.questions.add_from_library")}
                        </h1>
                        <div className="flex md:flex-row sm:flex-col">
                          <div className="flex relative">
                            <h2
                              className={`${!adminLibrary
                                ? "bg-coalColor text-primaryGreen rounded-md"
                                : "text-[#7C8289]"
                                } p-3 cursor-pointer w-full`}
                              onClick={() => {
                                if (user_package_check !== "Enterprise") {
                                  setPremiumGeneral(true);
                                } else if (adminLibrary !== false) {
                                  setAdminLibrary(false);
                                }
                              }}
                              style={{ fontFamily: "Archia Semibold" }}
                            >
                              {t("create_test.questions.your_library")}
                            </h2>
                            {user_package_check !== "Enterprise" && (
                              <span className="absolute top-0 right-0 -mt-2 -mr-2">
                                <img
                                  src={King}
                                  alt="Premium Icon"
                                  className="w-6 h-6"
                                />
                              </span>
                            )}
                          </div>
                          <div className="relative">
                            <h2
                              className={`${adminLibrary
                                ? "bg-coalColor text-primaryGreen rounded-md"
                                : "text-[#7C8289]"
                                } p-3 cursor-pointer`}
                              style={{ fontFamily: "Archia Semibold" }}
                              onClick={() => {
                                if (user_package_check !== "Enterprise") {
                                  setPremiumGeneral(true);
                                } else if (adminLibrary !== true) {
                                  setAdminLibrary(true);
                                }
                              }}
                            >
                              {t("create_test.questions.dexta_library")}
                            </h2>

                            {user_package_check !== "Enterprise" && (
                              <span className="absolute right-0 top-0 -mt-2">
                                <img
                                  src={King}
                                  alt="Premium Icon"
                                  className="w-6 h-6"
                                />
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="mt-7">
                        <div className="relative sm:w-full">
                          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <img src={SearhBar} className="w-5 h-5" />
                          </div>
                          <input
                            type="text"
                            className="bg-gray-50 border focus:ring-0 border-gray-300 text-gray-900 text-sm rounded block w-full p-3 px-10"
                            placeholder={t("create_test.questions.search_library")}
                            required
                            style={{ fontFamily: "Silka" }}
                            value={searchedValue}
                            onChange={(e) => setSearchedValue(e.target.value)}
                          />
                          <div className="flex items-center inset-y-0 right-0 absolute pr-1">
                            <div className="group relative left-10 m-12 flex justify-center">
                              <img
                                src={info}
                                data-tooltip-target="tooltip-default"
                                className="w-5 h-5 cursor-pointer"
                              />
                              <span className="absolute scale-0 right-0 bottom-11 rounded bg-gray-800 left-50 p-2 text-xs w-[20rem] text-white group-hover:scale-100">
                                {t("create_test.questions.search_tooltip")} {""}
                                {!adminLibrary
                                  ? t("create_test.questions.your_library_text")
                                  : t("create_test.questions.dexta_library_text")}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="grid xl:grid-cols-3 sm:grid-cols-1 2xl:grid-cols-3 gap-6 sm:px-0 md:px-0">
                      {!libraryLoading && (
                        <>
                          {libraryData?.data.map((i, index) => {
                            const isAdded = questions?.some(
                              (item) => item?.description === i?.description
                            );
                            const isDisabled = !questionSetName || !questionSetTime;
                            const disabledQuestions = questions?.length >= 50;
                            if (!adminLibrary && i?.is_custom_question) {
                              return null;
                            }

                            return (
                              <div
                                className="bg-[#F6F7F7] p-5 mt-5 border border-[#D3D5D8] rounded-lg"
                                key={index}
                              >
                                <div className="h-[70px] pr-3 text-sm 2xl:mt-3 sm:mt-3">
                                  <div
                                    style={{
                                      fontFamily: "Silka",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      display: "-webkit-box",
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: "vertical",
                                    }}
                                  >
                                    <div
                                      className="my-auto align-center flex"
                                      style={{
                                        fontFamily: "Silka",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        display: "-webkit-box",
                                        WebkitLineClamp: 3,
                                        WebkitBoxOrient: "vertical",
                                      }}
                                      dangerouslySetInnerHTML={{
                                        __html: removeInlineStyles(i?.description),
                                      }}
                                    />
                                  </div>
                                </div>

                                <div className="md:flex justify-between mt-3">
                                  <div className="md:flex flex-col gap-3 my-auto">
                                    <div className="flex flex-row gap-2">
                                      <VscLibrary className="w-5 h-5 my-auto" />
                                      <p>
                                        {adminLibrary ? (
                                          <>
                                            {i?.sections
                                              ? i?.sections.length !== 0
                                                ? i?.sections[0]?.name
                                                : t("create_test.questions.general")
                                              : t("create_test.questions.general")}
                                          </>
                                        ) : (
                                          t(
                                            "create_test.questions.your_library_label"
                                          )
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div className="relative">
                                      <PiDotsThreeOutlineFill
                                        className="w-6 h-6 cursor-pointer"
                                        id="labelID"
                                        onClick={() => {
                                          setDownloadDropdowns((prev) => ({
                                            ...prev,
                                            [index]: !prev[index],
                                          }));
                                        }}
                                      />
                                      {downloadDropdowns[index] && (
                                        <div
                                          id="education-drop"
                                          className={`downloadClass absolute z-20 border w-[15rem] ${isMobile ? "left-0" : "right-0"
                                            } border-coalColor top-10 h-auto overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)]`}
                                        >
                                          <ul
                                            id="downloadID"
                                            ref={downloadRef}
                                            onClick={() => {
                                              previewModule(i?.id);
                                              setDownloadDropdowns({});
                                            }}
                                            className="text-sm text-coalColor hover:bg-coalColor hover:text-white cursor-pointer"
                                            style={{ fontFamily: "Silka" }}
                                          >
                                            <li className="p-2">
                                              {t("create_test.questions.preview")}
                                            </li>
                                          </ul>
                                          {!adminLibrary && (
                                            <ul
                                              id="downloadID"
                                              ref={downloadRef}
                                              onClick={() => {
                                                setQuestionID(i?.id);
                                                setLastQuestionDelete(
                                                  questions?.length === 1
                                                );
                                                setDeleteModal(true);
                                              }}
                                              className="text-sm text-coalColor hover:bg-coalColor hover:text-white cursor-pointer"
                                              style={{ fontFamily: "Silka" }}
                                            >
                                              <li className="p-2">
                                                {t("create_test.questions.delete")}
                                              </li>
                                            </ul>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                    <div className="md:w-[125px] sm:w-[100px] ml-auto">
                                      {isAdded ? (
                                        <div className="relative">
                                          <CustomButton
                                            label={t(
                                              "create_test.questions.table.buttons.added"
                                            )}
                                            paddingY={
                                              createQuestionLoading &&
                                                loadingIndex === index
                                                ? "0.58rem"
                                                : "0.2rem"
                                            }
                                            bgColor="#C0FF06"
                                            textColor="#252E3A"
                                            textSize="text-base"
                                            borderCustom="border border-black"
                                          />
                                          <div className="tooltip w-[12rem] font-medium text-center">
                                            {t(
                                              "create_test.questions.table.buttons.tooltips.already_added"
                                            )}
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="relative">
                                          <CustomButton
                                            label={t(
                                              "create_test.questions.table.buttons.add"
                                            )}
                                            paddingY={
                                              createQuestionLoading &&
                                                loadingIndex === index
                                                ? "0.58rem"
                                                : "0.2rem"
                                            }
                                            bgColor="#252E3A"
                                            borderCustom={`border border-coalColor text-white ${fakeLoading && "cursor-not-allowed"
                                              }`}
                                            hoverBgColor="#C0FF06"
                                            hoverTextColor="#252E3A"
                                            textSize="text-base"
                                            disabledColor="#D3D5D8"
                                            disabledTextColor="#7C8289"
                                            onClickButton={() => {
                                              if (!fakeLoading) {
                                                handleAddCustomFromLibrary(
                                                  i,
                                                  index
                                                );
                                              }
                                            }}
                                            LoadingBtn={loadingIndex === index}
                                            disableField={
                                              isDisabled ||
                                              disabledQuestions ||
                                              (loadingIndex === index && fakeLoading)
                                            }
                                            disabledCheck={
                                              isDisabled ||
                                              disabledQuestions ||
                                              (loadingIndex === index && fakeLoading)
                                            }
                                            loadingText={t(
                                              "create_test.questions.table.buttons.adding"
                                            )}
                                          />
                                          {disabledQuestions && (
                                            <div className="tooltip w-[15rem] font-medium text-center">
                                              {t(
                                                "create_test.questions.table.buttons.tooltips.max_questions"
                                              )}
                                            </div>
                                          )}
                                          {isDisabled && (
                                            <div className="tooltip w-[15rem] font-medium text-center">
                                              {t(
                                                "create_test.questions.table.buttons.tooltips.fill_fields_first"
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </>
                      )}
                    </div>
                    {libraryData?.data?.length === 0 && (
                      <div className="mt-3 mb-5">
                        <div className="border border-[#FF5812] py-4 rounded">
                          <p
                            className="text-alertRed text-center"
                            style={{ fontFamily: "Silka Bold" }}
                          >
                            {t("create_test.questions.no_custom_questions")}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        )}

        <ToastContainer
          position="top-center"
          transition={Zoom}
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={true}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          enableMultiContainer={false}
          limit={1}
        />
      </div>
    </div >
  );
};

export default Questions;
