import React, { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getInvite } from "../hooks/getInvite";
import * as Yup from "yup";
import { useFormik } from "formik";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { updateUser } from "../hooks/updateUser";
import "../../../Components/Loading/Loading.css";
import Loader from "react-loader-spinner";
import { publicLink } from "../hooks/publicLink";
import "./confirmation.css";
import { setInviteText } from "../../../redux/reducers/Invite/InviteSlice";
import { useDispatch } from "react-redux";
import { GoArrowRight } from "react-icons/go";
import TextField from "../../../Components/Dexta/TextField/TextField";
import { ToastContainer, Zoom, toast } from "react-toastify";
import queryString from "query-string";
import PrivacyPolicyModal from "../../Legal-Documents/Candidate-Privacy-Policy/PrivacyPolicyModal";
import { buttonStyle } from "../../../utils/buttonstyling";
import { useTranslation } from "react-i18next";
import i18n from "../../../i18n";
import { LanguageSwitcher } from "../../../Helpers/LanguageSwitcher";

const Confirmation = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const [privacyModal, setPrivacyModal] = useState(false);
  const [privacy, setPrivacy] = useState(false);
  const module_info = { tests: 0, time: 0 };
  const [uuid, setuuid] = useState(false);
  const [company, setCompany] = useState("");
  const [isHovered, setIsHovered] = useState(false);
  const { t } = useTranslation();

  //#region Data fetch and api post/patch calls with react query mutation
  const { data, error, isLoading, isError } = useQuery(
    ["invite", id, parsed?.email],
    () => getInvite(id, parsed?.email),
    {
      retry: 1,
      onSuccess: (data) => {
        const theme = {
          logo: data?.recruiter_company_detail?.companyAvatar,
          video: data?.assessments?.user?.company?.companyVideo,
          color: data?.recruiter_company_detail?.color,
          name: data?.assessments?.user?.companyName,
          sec_color: data?.recruiter_company_detail?.sec_color,
        };
        localStorage.setItem("theme", JSON.stringify(theme));
      },
      onError: (error) => { },
    }
  );

  const { mutate, isLoading: mutateLoading } = useMutation(updateUser, {
    onSuccess: (res) => {
      console.log(res, "res");
      queryClient.invalidateQueries("users");
      module_info.tests = data?.totalSection;
      module_info.time = data?.totalTime;
      localStorage.setItem("user_role", res?.roles);
      localStorage.setItem("module_info", JSON.stringify(module_info));
      localStorage.setItem(
        "CP-CANDIDATE-FirstName",
        validation?.values?.firstName
      );
      localStorage.setItem(
        "CP-CANDIDATE-LastName",
        validation?.values?.lastName
      );
      localStorage.setItem("CP-CANDIDATE-Role", "user");
      localStorage.setItem("Exam", "Start");
      localStorage.setItem("Current_screen", "");
      navigate(
        `/information?firstName=${data?.user?.firstName != ""
          ? validation?.values?.firstName
          : validation?.values?.firstName
        }&lastName=${data?.user?.lastName != ""
          ? validation?.values?.lastName
          : validation?.values?.lastName
        }&Company=${data?.assessments?.user?.companyName}`
      );
    },
    onError: (response) => { },
  });

  const { mutate: publicmutate, isLoading: publicLoading } = useMutation(
    updateUser,
    {
      onSuccess: (res) => {
        queryClient.invalidateQueries("users");
        localStorage.setItem("user_role", res?.roles);
        localStorage.setItem("CP-CANDIDATE-EMAIL", validation?.values?.email);
        localStorage.setItem("CP-CANDIDATE-Role", "user");
        localStorage.setItem(
          "CP-CANDIDATE-FirstName",
          validation?.values?.firstName
        );
        localStorage.setItem(
          "CP-CANDIDATE-LastName",
          validation?.values?.lastName
        );
        localStorage.setItem("Exam", "Start");
        localStorage.setItem("Current_screen", "");
        navigate(
          `/information?firstName=${validation?.values?.firstName}&lastName=${validation?.values?.lastName}&Company=${company}`
        );
      },
      onError: (response) => {
        const errorMessage = response?.response?.data?.message;

        if (Array.isArray(errorMessage)) {
          setTimeout(() => {
            toast.error(t("confirmation.generic_error", errorMessage[0]), {
              toastId: "copy-success",
            });
          }, 500);
        } else if (typeof errorMessage === "string") {
          setTimeout(() => {
            toast.error(t("confirmation.generic_error", errorMessage), {
              toastId: "copy-success",
            });
          }, 500);
        } else {
          toast.error(t("confirmation.generic_error", "An error occurred."), {
            toastId: "copy-success",
          });
        }
      },
    }
  );
  const { mutate: assessmentMutate, isLoading: assessmentLoading } =
    useMutation(publicLink, {
      onSuccess: (response) => {
        console.log(response, "response");
        const user_data = JSON.parse(response?.old_data);
        queryClient.invalidateQueries("assessment");
        localStorage.setItem("CP-CANDIDATE-TOKEN", response?.accessToken);
        localStorage.setItem("CP-CANDIDATE-ID", response?.user?.id);
        localStorage.setItem("CP-CANDIDATE-EMAIL", validation?.values?.email);

        module_info.tests = response?.totalSection;
        module_info.time = response?.totalTime;
        localStorage.setItem("module_info", JSON.stringify(module_info));
        setCompany(response?.assessments?.user?.companyName);
        let data = JSON.stringify({
          firstName: user_data?.firstName,
          lastName: user_data?.lastName,
          email: response?.user?.email,
        });
        try {
          publicmutate(data);
        } catch {
          //don't need to catch error
        }
      },
      onError: (response) => {
        // if (response?.response?.data?.message[0].includes("Assessment link")) {
        //   navigate(`/expired?company=${data?.assessments?.user?.companyName}`)
        // }
        if (response?.response?.data?.message[0].includes("already")) {
          navigate(`/expired`);

          const theme = JSON.parse(localStorage.getItem("theme"));
          dispatch(
            setInviteText(
              t(
                "confirmation.already_completed",
                `You have already completed this test. If you have not completed this test, please contact and notify {{company}}`,
                { company: theme?.name || "the company" }
              )
            )
          );
          // dispatch(setInviteText(response?.response?.data?.message));
        }
      },
    });
  //#endregion

  //#region Handling form validations
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: data?.user?.firstName?.trim() || "",
      lastName: data?.user?.lastName?.trim() || "",
      email: data?.user?.email?.trim() || "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string()
        .min(1, "Too Short!")
        .max(50, "Too Long!")
        .required("First Name Required"),
      lastName: Yup.string()
        .min(1, "Too Short!")
        .max(50, "Too Long!")
        .required("Last Name Required"),
      email: Yup.string(),
    }),
    onSubmit: (values) => {
      const trimmedValues = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        email: values.email.trim(),
      };

      if (uuid && !data?.isInvitedByemail) {
        let mainData = {
          code: data?.assessments?.code,
          data: JSON.stringify({
            firstName: trimmedValues.firstName,
            lastName: trimmedValues.lastName,
            email: trimmedValues.email,
            attemptCode: id,
          }),
        };
        try {
          assessmentMutate(mainData);
        } catch {
          // Don't need to catch error
        }
      } else {
        let data = JSON.stringify({
          firstName: trimmedValues.firstName,
          lastName: trimmedValues.lastName,
        });
        try {
          mutate(data);
        } catch {
          // Don't need to catch error
        }
      }
    },
  });
  //#endregion

  //#region Function to check if we have UUID or token
  const CheckIfUUIDorToken = () => {
    const regexExp =
      /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi;
    const isValidUUID = regexExp.test(id);
    if (isValidUUID) {
      setuuid(true);
      localStorage.setItem("assessment", "link_invite");
    } else {
      setuuid(false);
      localStorage.setItem("assessment", "email_invite");
    }
  };
  //#endregion

  //#region Handling button colors
  const originalColor = data?.recruiter_company_detail.color;
  const secColor = data?.recruiter_company_detail.sec_color;
  const currentButtonStyle = buttonStyle(isHovered, originalColor, secColor);
  //#endregion

  //#region Handling hover focus and leave
  const handleHover = () => {
    setIsHovered(true);
  };

  const handleLeave = () => {
    setIsHovered(false);
  };
  //#endregion

  //#region useEffect to redirect user to different routes
  useEffect(() => {
    if (!isLoading) {
      if (error?.response?.data?.message[0].includes("Invalid")) {
        navigate("/invalid");
      }

      if (error?.response?.data?.message[0].includes("Assessment link")) {
        navigate(`/expired`);
        dispatch(
          setInviteText(
            t(
              "confirmation.invite_expired",
              "Your invite has expired. Please reach out to your company for further assistance."
            )
          )
        );
      }
      if (error?.response?.data?.message.includes("Assessment link")) {
        navigate("/expired");
        dispatch(
          setInviteText(
            t(
              "confirmation.invite_expired",
              "Your invite has expired. Please reach out to your company for further assistance."
            )
          )
        );
      }
      if (
        error?.response?.data?.message[0].includes("already") &&
        error?.response?.data?.message[0].includes("Link")
      ) {
        navigate(`/access-denied?code=${id}`);
        dispatch(setInviteText(error?.response?.data?.message));
      }
      if (
        error?.response?.data?.message[0].includes("Link") &&
        error?.response?.data?.message[0].includes("expired")
      ) {
        navigate(`/expired`);
        dispatch(
          setInviteText(
            t(
              "confirmation.invite_expired",
              "Your invite has expired. Please reach out to your company for further assistance."
            )
          )
        );
      }
      if (
        error?.response?.data?.message[0].includes("already") &&
        error?.response?.data?.message[0].includes("completed")
      ) {
        navigate(`/expired`);
        const theme = JSON.parse(localStorage.getItem("theme"));
        dispatch(
          setInviteText(
            t(
              "confirmation.already_completed",
              `You have already completed this test. If you have not completed this test, please contact and notify {{company}}`,
              { company: theme?.name || "the company" }
            )
          )
        );
      }
      if (
        error?.response?.data?.message.includes("candidate") &&
        error?.response?.data?.message.includes("found") &&
        error?.response?.data?.message.includes("invitation")
      ) {
        navigate(`/expired`);
        dispatch(
          setInviteText(
            t(
              "confirmation.no_invitation_found",
              "No invitation found for candidate. Please ask your recruiter to send a new invitation."
            )
          )
        );
      }
      if (error?.response?.data?.message?.includes("5")) {
        navigate(`/error`);
        dispatch(
          setInviteText(
            t(
              "confirmation.invite_limit",
              "You can invite only 5 candidates with your current plan!"
            )
          )
        );
      }
      if (error?.response?.data?.message[0].includes("not active")) {
        const theme = JSON.parse(localStorage.getItem("theme"));
        dispatch(
          setInviteText(
            t(
              "confirmation.test_not_active",
              `This test is not active. Please contact and notify {{company}}`,
              { company: theme?.name || "your company" }
            )
          )
        );
        navigate(`/error`);
      }
      if (
        error?.response?.data?.message.includes("Assessment") &&
        error?.response?.data?.message.includes("not") &&
        error?.response?.data?.message.includes("found")
      ) {
        const theme = JSON.parse(localStorage.getItem("theme"));
        dispatch( 
          setInviteText(
            t(
              "confirmation.test_not_active",
              `This test is not active. Please contact and notify {{company}}`,
              { company: theme?.name || "your company" }
            )
          )
        );
        navigate(`/error`);

      }
      if (data?.accessToken) {
        localStorage.setItem("CP-CANDIDATE-TOKEN", data.accessToken);
      }
      if (data?.recruiter_company_detail?.color) {
      }
      if (data?.user?.id) {
        localStorage.setItem("CP-CANDIDATE-ID", data.user.id);
      }
      if (data?.assessments?.id) {
        localStorage.setItem("CANDIDATE-ASSESSMENT-ID", data?.assessments?.id);
      }
    }
  }, [error?.response?.data?.message[0], data]);
  //#endregion

  //#region useEffect to check if we have uuid or token
  useEffect(() => {
    CheckIfUUIDorToken();
  }, []);
  //#endregion

  //#region useEffect to set Exam status
  useEffect(() => {
    localStorage.setItem("Exam", "");
  }, []);
  //#endregion

  document.title = t("confirmation.page_title", "Confirmation | Dexta");
  return (
    <>
      <nav
        className={`xl:static 2xl:fixed w-full z-20 top-0 left-0 py-2 bg-white`}
      >
        {!isLoading && (
          <div className="w-3/4 px-2 items-center py-2 text-xl text-white mx-auto">
            <div className="grid grid-cols-3 container mx-auto">
              <img
                src={data?.recruiter_company_detail?.companyAvatar}
                className=" object-contain"
                style={{ height: "70px" }}
                alt={t("confirmation.company_logo_alt", "Company logo")}
              />
              <div className="my-auto"></div>
              <div className="my-auto absolute right-8 translate-y-1/2 mx-auto">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        )}
      </nav>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        enableMultiContainer={false}
        limit={1}
      />
      <PrivacyPolicyModal
        privacyModal={privacyModal}
        setPrivacyModal={setPrivacyModal}
        privacy={privacy}
        setPrivacy={setPrivacy}
      />
      <div className="bg-bodyColor">
        <div className="mx-auto lg:container">
          {isLoading ? (
            <div className="loader-container-1 col-span-6">
              <div className="loader-1"></div>
            </div>
          ) : (
            <div className="flex justify-center h-screen">
              <div className="m-auto lg:w-1/2 rounded-md bg-white">
                <div className="rounded-lg">
                  <div className="p-8">
                    <div className="flex flex-col items-center">
                      <img
                        src={data?.recruiter_company_detail?.companyAvatar}
                        className=" object-contain w-[100px] h-10 mr-auto"
                        alt={t("confirmation.company_logo_alt", "Company logo")}
                      />
                      <div className="flex flex-col mr-auto">
                        <div
                          className="flex flex-row items-center"
                          style={{ fontFamily: "Silka" }}
                        >
                          <p className="text-black te xt-left font-bold mr-2 my-auto">
                            {t("confirmation.role_label", "Role")}{" "}
                            <span className="font-bold my-auto text-gray-400">
                              {data?.assessments?.name}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <p
                      className="mt-5 text-black font-bold"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("confirmation.please_confirm")}
                    </p>
                    <form
                      onSubmit={(e) => {
                        e.preventDefault();
                        validation.handleSubmit();
                        return false;
                      }}
                    >
                      <div className="mt-5">
                        <TextField
                          type="text"
                          name="firstName"
                          label={t("confirmation.first_name", "First name")}
                          value={validation.values.firstName || ""}
                          rounded="rounded-md"
                          border={`border border-[#D3D5D8] focus-within:border focus-within:border-coalColor ${validation.values.firstName && "border-coalColor"
                            }`}
                          onChange={validation.handleChange}
                          onBlur={validation.handleBlur}
                          placeholder={t(
                            "confirmation.enter_first_name",
                            "Enter your first name"
                          )}
                        />
                      </div>
                      {validation.touched.firstName &&
                        validation.errors.firstName ? (
                        <p className="text-rose-500 text-xs ml-2">
                          {t(
                            "confirmation.first_name_required",
                            validation.errors.firstName
                          )}
                        </p>
                      ) : null}

                      <div className="mt-5">
                        <TextField
                          type="text"
                          name="lastName"
                          label={t("confirmation.last_name", "Last name")}
                          value={validation.values.lastName || ""}
                          rounded="rounded-md"
                          border={`border border-[#D3D5D8] focus-within:border focus-within:border-coalColor ${validation.values.lastName && "border-coalColor"
                            }`}
                          onChange={validation.handleChange}
                          onBlur={validation.handleBlur}
                          placeholder={t(
                            "confirmation.enter_last_name",
                            "Enter your last name"
                          )}
                        />
                      </div>
                      {validation.touched.lastName &&
                        validation.errors.lastName ? (
                        <p className="text-rose-500 text-xs ml-2">
                          {t(
                            "confirmation.last_name_required",
                            validation.errors.lastName
                          )}
                        </p>
                      ) : null}

                      {uuid && !data?.isInvitedByemail && (
                        <>
                          <div className="mt-5">
                            <TextField
                              type="text"
                              name="email"
                              label={t("confirmation.email", "Email")}
                              value={validation.values.email || ""}
                              rounded="rounded-md"
                              border={`border border-[#D3D5D8] focus-within:border focus-within:border-coalColor ${validation.values.email && "border-coalColor"
                                }`}
                              onChange={validation.handleChange}
                              onBlur={validation.handleBlur}
                              placeholder={t(
                                "confirmation.enter_email",
                                "Enter your email"
                              )}
                            />
                          </div>
                          {validation.touched.email &&
                            validation.errors.email ? (
                            <p className="text-rose-500 text-xs ml-2">
                              {t(
                                "confirmation.email_required",
                                validation.errors.email
                              )}
                            </p>
                          ) : null}
                        </>
                      )}
                      <div className="flex flex-row p-2 mt-3">
                        <label
                          className="relative flex cursor-pointer items-center rounded-full"
                          data-ripple-dark="true"
                        >
                          <input
                            id="ripple-on"
                            type="checkbox"
                            required
                            checked={privacy}
                            onInvalid={(e) => {
                              e.target.setCustomValidity(
                                t(
                                  "confirmation.privacy_policy_required",
                                  "Please review Privacy Policy and check box to proceed"
                                )
                              );
                            }}
                            onInput={(e) => {
                              e.target.setCustomValidity("");
                            }}
                            onChange={() => {
                              setPrivacy(!privacy);
                            }}
                            className={`peer relative h-5 w-5 cursor-pointer checked:bg-coalColor appearance-none rounded border-[1px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 border-gray-600 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor hover:before:opacity-50`}
                          />
                          <div
                            className={`pointer-events-none absolute top-2/4 left-2/4 text-primaryGreen -translate-y-2/4 -translate-x-2/4 opacity-0 transition-opacity peer-checked:opacity-100`}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3.5 w-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              stroke="currentColor"
                              strokeWidth="1"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              ></path>
                            </svg>
                          </div>
                        </label>
                        <p
                          style={{ fontFamily: "Silka" }}
                          className="ml-2 sm:text-xs md:text-sm text-[#6B6B6B]"
                        >
                          {t(
                            "confirmation.privacy_policy_text",
                            "I have read and I accept the "
                          )}
                          <b
                            onClick={() => {
                              setPrivacyModal(true);
                            }}
                            style={{
                              color: data?.recruiter_company_detail.sec_color,
                            }}
                            className="cursor-pointer"
                          >
                            {t("confirmation.privacy_policy", "Privacy Policy")}
                          </b>
                        </p>
                      </div>
                      {uuid && !data?.isInvitedByemail ? (
                        <button
                          className={`inline-flex items-center w-full justify-center px-4 mt-5 py-4 text-white text-base font-medium rounded-md`}
                          style={currentButtonStyle}
                          onMouseEnter={handleHover}
                          onMouseLeave={handleLeave}
                          type="submit"
                          disabled={
                            validation.values.firstName === "" ||
                            validation.values.lastName === "" ||
                            validation.values.email === ""
                          }
                        >
                          {assessmentLoading || publicLoading ? (
                            <span className="flex items-center justify-center">
                              <Loader
                                type="Oval"
                                color={data?.recruiter_company_detail.sec_color}
                                height={20}
                                width={20}
                              />
                              <span className="ml-2">
                                {t("confirmation.continuing", "Continuing...")}
                              </span>
                            </span>
                          ) : (
                            t("confirmation.continue", "Continue")
                          )}
                          <GoArrowRight
                            alt={t(
                              "confirmation.continue_icon_alt",
                              "Continue icon"
                            )}
                            className="w-5 h-5 ml-2 icon-image"
                          />
                        </button>
                      ) : (
                        <button
                          className={`inline-flex items-center w-full justify-center px-4 mt-5 py-4 text-white text-base font-medium rounded-md border border-${data?.recruiter_company_detail.sec_color}`}
                          style={currentButtonStyle}
                          onMouseEnter={handleHover}
                          onMouseLeave={handleLeave}
                          type="submit"
                          disabled={
                            validation.values.firstName === "" ||
                            validation.values.lastName === ""
                          }
                        >
                          {mutateLoading ? (
                            <span className="flex items-center justify-center">
                              <Loader
                                type="Oval"
                                color={data?.recruiter_company_detail.sec_color}
                                height={20}
                                width={20}
                              />
                              <span className="ml-2">
                                {t("confirmation.continuing", "Continuing...")}
                              </span>
                            </span>
                          ) : (
                            t("confirmation.continue", "Continue")
                          )}
                          <GoArrowRight
                            alt={t(
                              "confirmation.continue_icon_alt",
                              "Continue icon"
                            )}
                            className="w-5 h-5 ml-2 icon-image"
                          />
                        </button>
                      )}
                    </form>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Confirmation;
