export const dummy_sections = [
  {
    name: "Agile Ways of Working L1",
    experience: "Beginner",
    time: "59s / 10m",
  },
  {
    name: "Process Reengineering L1",
    experience: "Intermediate",
    time: "30s / 10m",
  },
  {
    name: "Robotics Process Automation L1",
    experience: "Advanced",
    time: "6m / 10m",
  },
  {
    name: "Agile Ways of Working L2",
    experience: "Advanced",
    time: "1m / 10m",
  },
  {
    name: "Agile Ways of Working L3",
    experience: "Beginner",
    time: "5m / 10m",
  },
];

export const experience = [
  {
    name: "Entry level",
    value: "Beginner",
  },
  {
    name: "Mid level",
    value: "Intermediate",
  },
  {
    name: "Senior level",
    value: "Advanced",
  },
  {
    name: "All levels",
    value: "all",
  },
];

export const work_arrangements = [
  {
    name: "Remote",
    value: "remote",
  },
  {
    name: "Onsite",
    value: "onsite",
  },
  {
    name: "Hybrid",
    value: "hybrid",
  },
];

export const assessments = [
  {
    name: "All",
    title: "",
  },
  {
    name: "Active",
    title: "active",
  },
  {
    name: "Draft",
    title: "draft",
  },
  {
    name: "Archived",
    title: "archived",
  },
  {
    name: "Deactivated",
    title: "deactivated",
  },
];

export const getSteps = (t) => [
  {
    title: t("homeSteps.createNewTestsTitle"),
    element: "#assessment1",
    intro: (
      <div style={{ fontFamily: "Silka" }}>
        <p>{t("homeSteps.createNewTestsIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("homeSteps.createNewTestsList1")}</li>
          <li>{t("homeSteps.createNewTestsList2")}</li>
          <li>{t("homeSteps.createNewTestsList3")}</li>
          <li>{t("homeSteps.createNewTestsList4")}</li>
          <li>{t("homeSteps.createNewTestsList5")}</li>
        </ul>
      </div>
    ),
  },
  {
    title: t("homeSteps.testsTitle"),
    element: "#assessment2",
    intro: (
      <div>
        <p>{t("homeSteps.testsIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("homeSteps.testsList1")}</li>
          <li>{t("homeSteps.testsList2")}</li>
          <li>{t("homeSteps.testsList3")}</li>
        </ul>
      </div>
    ),
  },
  {
    title: t("homeSteps.filterTestsTitle"),
    element: "#assessment3",
    intro: (
      <div>
        <p>{t("homeSteps.filterTestsIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("homeSteps.filterTestsList1")}</li>
          <li>{t("homeSteps.filterTestsList2")}</li>
          <li>{t("homeSteps.filterTestsList3")}</li>
        </ul>
      </div>
    ),
  },
  {
    title: t("homeSteps.searchTestsTitle"),
    element: "#assessment4",
    intro: (
      <div>
        <p>{t("homeSteps.searchTestsIntro")}</p>
      </div>
    ),
  },
];

export const roles = [
  {
    title: "All Roles",
    value: "",
  },
  {
    title: "Owner",
    value: "owner",
  },
  {
    title: "Admin",
    value: "admin",
  },
  {
    title: "Editor",
    value: "recruiter",
  },
  {
    title: "Viewer",
    value: "hiring-manager",
  },
];

export const graph_filters = [
  {
    title: "Your best performing candidate",
  },
  {
    title: "Your candidate pool average",
  },
  {
    title: "Dexta candidate pool average",
  },
  // {
  //   title: "Dexta best performing candidate",
  // },
];

export const type_mail = [
  {
    title: "Accept",
    value: "Send offer to candidate",
  },
  {
    title: "Reject",
    value: "Reject candidate",
  },
];
