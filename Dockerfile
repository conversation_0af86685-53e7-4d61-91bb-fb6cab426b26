FROM node:22 as builder

WORKDIR /platform/app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files from the builder stage to nginx html directory under /app
COPY --from=builder /platform/app/build /usr/share/nginx/html/app

# Copy nginx configuration to set up reverse proxy
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
