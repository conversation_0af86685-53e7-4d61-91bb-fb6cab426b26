import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import "./Auth.css";
import { useNavigate } from "react-router-dom";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { forgotEmail } from "./hooks/forgotEmail";
import { toast } from "react-toastify";
import { ToastContainer, Zoom } from "react-toastify";
import MainLogo from "../../Dexta_assets/LoginLogov4.png";
import Email from "../../Dexta_assets/email-image.png";
import TextField from "../../Components/Dexta/TextField/TextField";
import CustomButton from "../../Components/CustomButton/CustomButton";
import { FaArrowRightLong } from "react-icons/fa6";
import { useTranslation } from "react-i18next";

const ForgetPassword = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [toastError, setToasterror] = useState("");
  const [toastSuccess, setToastSuccess] = useState(false);

  //#region api call for forget passwowrd
  const { mutate: mutateEmail, isLoading: emailLoading } = useMutation(
    forgotEmail,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/auth/forgot-password");
        if (response.message === "Reset password email sent")
          setToastSuccess(t("reset_password_email_sent"));
        navigate(
          `/forget-password-email-sent/?email=${validation?.values?.email}`
        );
      },
      onError: (error) => {
        if (error.response.data.message[0] != "")
          setToasterror(t("invalid_email"));
      },
    }
  );
  //#endregion

  //#region success and error toasts
  useEffect(() => {
    if (toastSuccess != "") {
      toast.success(toastSuccess, {
        toastId: "copy-success",
      });
    }
    setTimeout(() => {
      setToastSuccess("");
    }, 1000);
  }, [toastSuccess]);

  useEffect(() => {
    if (toastError != "") {
      toast.error(toastError, {
        toastId: "copy-error",
      });
    }
    setTimeout(() => {
      setToasterror("");
    }, 1000);
  }, [toastError]);
  //#endregion

  //#region formik validations and handle submit function
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      email: "",
    },
    validationSchema: Yup.object({
      email: Yup.string().email(t("invalid_email")).required(t("required_field")),
    }),
    onSubmit: (values) => {
      let data = JSON.stringify({
        email: values?.email,
      });
      try {
        mutateEmail(data);
      } catch (error) {
        //onError will automatically detect
      }
    },
  });
  //#endregion

  document.title = "Forget Password | Dexta";

  return (
    <React.Fragment>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <div className="sm:grid-cols-1 lg:grid-cols-2 grid bg-bgAuth h-screen">
        <div className="bg-black sm:hidden lg:block">
          <img
            src={MainLogo}
            className="w-full lg:h-full xl:h-fit lg:object-cover"
          />
        </div>

        <div className="bg-bgAuth flex justify-center flex-col md:pb-40 relative">
          <div className="sm:w-3/4 lg:w-1/2 mx-auto mt-20 pt-4">
            <h1
              className="text-2xl text-left"
              style={{ fontFamily: "Archia Semibold" }}
            >
              {t("forgot_password")}
            </h1>
            <div style={{ fontFamily: "Silka" }} className="text-sm mt-6">
              <span>
                {t("check_email")}
              </span>
            </div>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                validation.handleSubmit();
                return false;
              }}
              id="forget-password"
            >
              <div className="mt-6">
                <div className="h-[4rem]">
                  <TextField
                    name="email"
                    id="email"
                    type="email"
                    placeholder={t("enter_email") || "Work email"}
                    onChange={validation.handleChange}
                    onBlur={validation.handleBlur}
                    value={validation.values.email || ""}
                    imageUrl={Email}
                  />
                  {validation.touched.email && validation.errors.email ? (
                    <div className="ml-1">
                      <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                        {validation.errors.email}
                      </p>
                    </div>
                  ) : null}
                </div>
              </div>
              
              <div className="flex items-center gap-6 mt-8 flex-wrap">
                <div className="flex-shrink-0 w-full">
                  <CustomButton
                    label={t("reset_password") || "RESET PASSWORD"}
                    borderCustom="border border-black text-white"
                    hoverBgColor="#C0FF06"
                    hoverTextColor="#252E3A"
                    paddingY="0.7rem"
                    bgColor="#252E3A"
                    iconR={FaArrowRightLong}
                    noMarginIcon={false}
                    autoLeftMargin="ml-auto"
                    textMarginBotton="ml-auto"
                    LoadingBtn={emailLoading}
                    loadingText="SENDING EMAIL"
                    customWidth="w-auto px-6"
                  />
                </div>
                
                <p
                  className="sm:text-xs md:text-sm cursor-pointer underline whitespace-nowrap"
                  style={{ fontFamily: "Silka Light" }}
                  onClick={() => navigate("/login")}
                >
                  {t("return_to_login") || "Return to login"}
                </p>
              </div>
            </form>

          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default ForgetPassword;
