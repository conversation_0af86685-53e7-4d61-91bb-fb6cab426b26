name: s3-depl-ksa-app

on:
  push:
    branches: [ ksa_main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Install Yarn
        run: npm install --global yarn

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Clean and Install Dependencies
        env:
          CI: false
        run: |
          yarn install
          yarn add ajv
          yarn build

      - name: Build React App
        env:
          TSC_COMPILE_ON_ERROR: true
          REACT_APP_STATIC_SITE: https://ksa.app.dexta.io
          REACT_APP_Server: https://ksa.app.dexta.io/api
          REACT_APP_STRIPE_PUBLISHABLE_KEY: ${{ 'pk_live_51Pl5gYCtrD5MqWCp1et0RIW5VXkrXNWjNBPv61lENryswSTTvbQK8pF9ngqwasjtEEhftT5gBWuTl8SsOVeAk7c200KxWqhVco' }}
          REACT_APP_RECAPTCHA_SITE_KEY: ${{ secrets.REACT_APP_RECAPTCHA_SITE_KEY }}
          REACT_APP_LANGUAGE_SWITCH_ACCESS: 'NO'
          REACT_APP_CANDIDATE_NAVBAR: 'My tests'
          REACT_APP_CANDIDATE_DASHBOARD_HEADING: 'My tests'
          REACT_APP_CANDIDATE_DASHBOARD_DESCRIPTION: 'Your test will be displayed here once you have taken a test'
          REACT_APP_COMPLETED_TESTS_VISIBILITY: 'NO'

        run: |
          yarn build

      - name: Deploy app build to S3 bucket
        run: aws s3 sync ./build/ s3://dexta-app-ksa --delete
