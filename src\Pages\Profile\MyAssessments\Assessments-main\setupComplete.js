import React from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

const SetupCompleted = (props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <div className="sm:px-2 pb-20">
      <div className="bg-white rounded-lg">
        <div className="p-5 pt-5 text-coalColor">
          <h2
            className="text-coalColor mt-3"
            style={{ fontFamily: "Archia Semibold" }}
          >
            {t("setup_completed.thank_you")}
          </h2>
          <p className="mt-10" style={{ fontFamily: "Silka" }}>
            {t("setup_completed.invites_sent")}
          </p>
          <p
            className="underline mt-10 text-md cursor-pointer"
            onClick={() => {
              navigate("/dashboard");
              localStorage.removeItem("current_module");
            }}
          >
            {t("setup_completed.my_tests")}
          </p>
        </div>
      </div>
    </div>
  );
};
export default SetupCompleted;
